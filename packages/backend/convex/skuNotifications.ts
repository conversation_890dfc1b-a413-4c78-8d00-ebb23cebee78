import { v } from "convex/values";
import { mutation, query, internalMutation, internalAction } from "./_generated/server";
import { ConvexError } from "convex/values";
import { getAuthUser, requireAuth } from "./lib/auth_utils";
import { sendEmail } from "./lib/email";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

/**
 * Create a new SKU subscription
 */
export const createSkuSubscription = mutation({
  args: {
    sku: v.string(),
    brand: v.optional(v.string()),
    category: v.optional(v.string()),
    maxPrice: v.optional(v.number()),
    minCondition: v.optional(v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    )),
  },
  returns: v.object({
    success: v.boolean(),
    subscriptionId: v.id("skuSubscriptions"),
    message: v.string(),
  }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Check if user already has a subscription for this SKU
    const existingSubscription = await ctx.db
      .query("skuSubscriptions")
      .withIndex("by_user_sku", (q) => 
        q.eq("userId", user._id).eq("sku", args.sku.trim().toUpperCase())
      )
      .first();

    if (existingSubscription) {
      throw new ConvexError("You already have a subscription for this SKU");
    }

    // Check subscription limits (free users get 5, paid users get unlimited)
    const userSubscriptionCount = await ctx.db
      .query("skuSubscriptions")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .collect();

    const fullUser = await ctx.db.get(user._id);
    const isSubscribed = fullUser?.subscriptionStatus === "active";
    const maxSubscriptions = isSubscribed ? 100 : 5;

    if (userSubscriptionCount.length >= maxSubscriptions) {
      throw new ConvexError(
        isSubscribed 
          ? "You have reached the maximum number of SKU subscriptions (100)" 
          : "Free users can only have 5 SKU subscriptions. Upgrade to add more."
      );
    }

    const now = Date.now();
    const subscriptionId = await ctx.db.insert("skuSubscriptions", {
      userId: user._id,
      sku: args.sku.trim().toUpperCase(),
      brand: args.brand?.trim(),
      category: args.category,
      maxPrice: args.maxPrice,
      minCondition: args.minCondition,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      triggerCount: 0,
    });

    // Log analytics event
    await ctx.db.insert("analytics", {
      eventType: "sku_subscription_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: subscriptionId,
        category: "notifications",
        revenue: 0,
      },
    });

    return {
      success: true,
      subscriptionId,
      message: `SKU subscription created successfully for ${args.sku}`,
    };
  },
});

/**
 * Get user's SKU subscriptions
 */
export const getUserSkuSubscriptions = query({
  args: {
    includeInactive: v.optional(v.boolean()),
  },
  returns: v.array(v.object({
    _id: v.id("skuSubscriptions"),
    sku: v.string(),
    brand: v.optional(v.string()),
    category: v.optional(v.string()),
    maxPrice: v.optional(v.number()),
    minCondition: v.optional(v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    )),
    isActive: v.boolean(),
    createdAt: v.number(),
    lastTriggered: v.optional(v.number()),
    triggerCount: v.number(),
  })),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    let query = ctx.db
      .query("skuSubscriptions")
      .withIndex("by_userId", (q) => q.eq("userId", user._id));

    if (!args.includeInactive) {
      query = query.filter((q) => q.eq(q.field("isActive"), true));
    }

    return await query.collect();
  },
});

/**
 * Update SKU subscription
 */
export const updateSkuSubscription = mutation({
  args: {
    subscriptionId: v.id("skuSubscriptions"),
    maxPrice: v.optional(v.number()),
    minCondition: v.optional(v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    )),
    isActive: v.optional(v.boolean()),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
  }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription) {
      throw new ConvexError("Subscription not found");
    }

    if (subscription.userId !== user._id) {
      throw new ConvexError("You can only update your own subscriptions");
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.maxPrice !== undefined) updateData.maxPrice = args.maxPrice;
    if (args.minCondition !== undefined) updateData.minCondition = args.minCondition;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;

    await ctx.db.patch(args.subscriptionId, updateData);

    return {
      success: true,
      message: "Subscription updated successfully",
    };
  },
});

/**
 * Delete SKU subscription
 */
export const deleteSkuSubscription = mutation({
  args: {
    subscriptionId: v.id("skuSubscriptions"),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
  }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription) {
      throw new ConvexError("Subscription not found");
    }

    if (subscription.userId !== user._id) {
      throw new ConvexError("You can only delete your own subscriptions");
    }

    await ctx.db.delete(args.subscriptionId);

    return {
      success: true,
      message: "Subscription deleted successfully",
    };
  },
});

/**
 * Internal function to check for SKU matches when a product is created/updated
 */
export const checkSkuSubscriptions = internalMutation({
  args: {
    productId: v.id("products"),
    sku: v.string(),
    brand: v.string(),
    category: v.string(),
    price: v.number(),
    condition: v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const searchSku = args.sku.trim().toUpperCase();
    
    // Find all active subscriptions for this SKU
    const subscriptions = await ctx.db
      .query("skuSubscriptions")
      .withIndex("by_sku_active", (q) => 
        q.eq("sku", searchSku).eq("isActive", true)
      )
      .collect();

    for (const subscription of subscriptions) {
      // Check if subscription criteria are met
      let shouldNotify = true;

      // Check brand filter
      if (subscription.brand && subscription.brand.toLowerCase() !== args.brand.toLowerCase()) {
        shouldNotify = false;
      }

      // Check category filter
      if (subscription.category && subscription.category !== args.category) {
        shouldNotify = false;
      }

      // Check max price
      if (subscription.maxPrice && args.price > subscription.maxPrice) {
        shouldNotify = false;
      }

      // Check minimum condition
      if (subscription.minCondition) {
        const conditionOrder = ["fair", "good", "very_good", "excellent", "like_new", "new"];
        const minConditionIndex = conditionOrder.indexOf(subscription.minCondition);
        const productConditionIndex = conditionOrder.indexOf(args.condition);
        
        if (productConditionIndex < minConditionIndex) {
          shouldNotify = false;
        }
      }

      if (shouldNotify) {
        // Schedule notification sending
        await ctx.scheduler.runAfter(0, internal.skuNotifications.sendSkuNotification, {
          subscriptionId: subscription._id,
          productId: args.productId,
        });
      }
    }

    return null;
  },
});

/**
 * Internal function to send SKU availability notification
 */
export const sendSkuNotification = internalAction({
  args: {
    subscriptionId: v.id("skuSubscriptions"),
    productId: v.id("products"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Get subscription details
    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription || !subscription.isActive) {
      return null;
    }

    // Get user details
    const user = await ctx.db.get(subscription.userId);
    if (!user) {
      return null;
    }

    // Check if user has SKU alerts enabled
    const preferences = await ctx.db
      .query("userPreferences")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    const skuAlertsEnabled = preferences?.emailNotifications?.skuAlerts ?? true;
    if (!skuAlertsEnabled) {
      return null;
    }

    // Get product details
    const product = await ctx.db.get(args.productId);
    if (!product) {
      return null;
    }

    const now = Date.now();

    try {
      // Create in-app notification
      await ctx.db.insert("notifications", {
        userId: user._id,
        type: "sku_available",
        title: `🔔 SKU Alert: ${subscription.sku} is available!`,
        message: `${product.brand} ${product.title} is now available for $${product.price}`,
        data: {
          productId: product._id,
          sku: subscription.sku,
          price: product.price,
          condition: product.condition,
        },
        read: false,
      });

      // Send email notification with inline HTML template
      const emailHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>SKU Alert: ${subscription.sku} is Available</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background-color: #f8f9fa; padding: 30px; border-radius: 10px;">
            <h1 style="color: #302923; margin-bottom: 20px;">🔔 SKU Alert!</h1>
            
            <p style="font-size: 16px; margin-bottom: 20px;">Hi ${user.name},</p>
            
            <p style="font-size: 16px; margin-bottom: 20px;">
              Great news! The SKU <strong>${subscription.sku}</strong> you've been waiting for is now available:
            </p>
            
            <div style="background-color: white; padding: 20px; border-radius: 8px; border-left: 4px solid #302923; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #302923;">${product.brand} ${product.title}</h3>
              <p style="margin: 10px 0;"><strong>SKU:</strong> ${subscription.sku}</p>
              <p style="margin: 10px 0;"><strong>Price:</strong> $${product.price.toFixed(2)}</p>
              <p style="margin: 10px 0;"><strong>Condition:</strong> ${product.condition.replace('_', ' ')}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/marketplace/product/${product._id}" 
                 style="background-color: #302923; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                View Product
              </a>
            </div>
            
            <p style="font-size: 14px; color: #666; margin-top: 30px;">
              This item was added to our marketplace and matches your SKU alert preferences. 
              Act fast as luxury items tend to sell quickly!
            </p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="font-size: 12px; color: #999;">
              You received this email because you set up a SKU alert for ${subscription.sku}. 
              You can manage your SKU alerts in your account settings.
            </p>
          </div>
        </body>
        </html>
      `;

      await sendEmail(ctx, {
        to: user.email,
        subject: `🔔 SKU Alert: ${subscription.sku} is now available!`,
        react: emailHtml,
      });

      // Update subscription trigger count and last triggered
      await ctx.db.patch(subscription._id, {
        triggerCount: subscription.triggerCount + 1,
        lastTriggered: now,
        updatedAt: now,
      });

      console.log(`✅ SKU notification sent to ${user.email} for SKU ${subscription.sku}`);

    } catch (error) {
      console.error("Failed to send SKU notification:", error);
    }

    return null;
  },
});