import { v } from "convex/values";
import { query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get products by marketplace type with filtering and pagination
 */
export const getProductsByMarketplace = query({
  args: {
    marketplaceType: v.union(
      v.literal("luxury"),
      v.literal("watches"),
      v.literal("jewelry"),
      v.literal("cars"),
      v.literal("art"),
      v.literal("fashion"),
      v.literal("sneakers"),
      v.literal("handbags")
    ),
    category: v.optional(v.string()),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brand: v.optional(v.string()),
    condition: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("products"),
    _creationTime: v.number(),
    sellerId: v.id("users"),
    title: v.string(),
    description: v.string(),
    price: v.number(),
    marketplaceType: v.optional(v.string()),
    category: v.string(),
    brand: v.string(),
    condition: v.string(),
    images: v.array(v.string()),
    status: v.string(),
    views: v.number(),
    favorites: v.number(),
    // Additional optional fields that might exist in products
    color: v.optional(v.string()),
    size: v.optional(v.string()),
    material: v.optional(v.string()),
    model: v.optional(v.string()),
    year: v.optional(v.number()),
    originalPrice: v.optional(v.number()),
    sku: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    sellerInfo: v.optional(v.object({
      name: v.string(),
      profileImage: v.optional(v.string()),
      isVerified: v.optional(v.boolean()),
    })),
  })),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    
    // Check if user is authenticated
    if (!user) {
      throw new ConvexError("Authentication required to view marketplace");
    }
    
    // Check if user has valid subscription (active or trial)
    const hasValidSubscription = user.subscriptionStatus === "active" || 
                                 user.subscriptionStatus === "trial" ||
                                 (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());
    
    if (!hasValidSubscription) {
      throw new ConvexError("Active subscription required to view marketplace");
    }

    // Build query with marketplace type filter
    // First try to get products with the specific marketplace type
    let products = await ctx.db
      .query("products")
      .withIndex("by_marketplaceType_and_status", (q) => 
        q.eq("marketplaceType", args.marketplaceType).eq("status", "active")
      )
      .collect();

    // If no products found with marketplace type, also include products without marketplace type (legacy products)
    if (products.length === 0) {
      const allActiveProducts = await ctx.db
        .query("products")
        .withIndex("by_status", (q) => q.eq("status", "active"))
        .collect();
      
      // Filter for products without marketplace type or with the requested marketplace type
      products = allActiveProducts.filter(p => 
        !p.marketplaceType || p.marketplaceType === args.marketplaceType
      );
    }

    // Filter by category if provided
    if (args.category) {
      products = products.filter(p => p.category === args.category);
    }

    // Filter by price range
    if (args.minPrice !== undefined) {
      products = products.filter(p => p.price >= args.minPrice!);
    }
    if (args.maxPrice !== undefined) {
      products = products.filter(p => p.price <= args.maxPrice!);
    }

    // Filter by brand
    if (args.brand) {
      products = products.filter(p => 
        p.brand.toLowerCase().includes(args.brand!.toLowerCase())
      );
    }

    // Filter by condition
    if (args.condition) {
      products = products.filter(p => p.condition === args.condition);
    }

    // Sort by creation time (newest first)
    products.sort((a, b) => b._creationTime - a._creationTime);

    // Apply limit
    const limit = args.limit || 50;
    products = products.slice(0, limit);

    // Enrich with seller information and image URLs
    const enrichedProducts = await Promise.all(
      products.map(async (product) => {
        // Get seller info
        const seller = await ctx.db.get(product.sellerId);
        
        // Get image URLs
        const imageUrls = await Promise.all(
          product.images.map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId as Id<"_storage">);
            return url || "";
          })
        );

        return {
          _id: product._id,
          _creationTime: product._creationTime,
          sellerId: product.sellerId,
          title: product.title,
          description: product.description,
          price: product.price,
          marketplaceType: product.marketplaceType || "luxury", // Provide default for legacy products
          category: product.category as string,
          brand: product.brand as string,
          condition: product.condition as string,
          status: product.status as string,
          views: product.views,
          favorites: product.favorites,
          images: imageUrls.filter(url => url !== ""),
          // Include optional fields if they exist
          color: product.color,
          size: product.size,
          material: product.material,
          model: product.model,
          year: product.year,
          originalPrice: product.originalPrice,
          sku: product.sku,
          tags: product.tags,
          sellerInfo: seller ? {
            name: seller.name,
            profileImage: seller.profileImage,
            isVerified: seller.isVerified,
          } : undefined,
        };
      })
    );

    return enrichedProducts;
  },
});

/**
 * Get marketplace statistics
 */
export const getMarketplaceStats = query({
  args: {
    marketplaceType: v.union(
      v.literal("luxury"),
      v.literal("watches"),
      v.literal("jewelry"),
      v.literal("cars"),
      v.literal("art"),
      v.literal("fashion"),
      v.literal("sneakers"),
      v.literal("handbags")
    ),
  },
  returns: v.object({
    totalProducts: v.number(),
    totalSellers: v.number(),
    averagePrice: v.number(),
    categoryBreakdown: v.array(v.object({
      category: v.string(),
      count: v.number(),
    })),
    brandBreakdown: v.array(v.object({
      brand: v.string(),
      count: v.number(),
    })),
  }),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    
    // Check if user is authenticated
    if (!user) {
      throw new ConvexError("Authentication required to view marketplace stats");
    }
    
    // Check if user has valid subscription (active or trial)
    const hasValidSubscription = user.subscriptionStatus === "active" || 
                                 user.subscriptionStatus === "trial" ||
                                 (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());
    
    if (!hasValidSubscription) {
      throw new ConvexError("Active subscription required to view marketplace stats");
    }

    // Get all active products for this marketplace
    let products = await ctx.db
      .query("products")
      .withIndex("by_marketplaceType_and_status", (q) => 
        q.eq("marketplaceType", args.marketplaceType).eq("status", "active")
      )
      .collect();

    // If no products found with marketplace type, also include products without marketplace type (legacy products)
    if (products.length === 0) {
      const allActiveProducts = await ctx.db
        .query("products")
        .withIndex("by_status", (q) => q.eq("status", "active"))
        .collect();
      
      // Filter for products without marketplace type or with the requested marketplace type
      products = allActiveProducts.filter(p => 
        !p.marketplaceType || p.marketplaceType === args.marketplaceType
      );
    }

    // Calculate statistics
    const totalProducts = products.length;
    const uniqueSellers = new Set(products.map(p => p.sellerId)).size;
    const averagePrice = products.length > 0 
      ? products.reduce((sum, p) => sum + p.price, 0) / products.length 
      : 0;

    // Category breakdown
    const categoryCount: Record<string, number> = {};
    products.forEach(p => {
      categoryCount[p.category] = (categoryCount[p.category] || 0) + 1;
    });
    const categoryBreakdown = Object.entries(categoryCount)
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count);

    // Brand breakdown (top 10)
    const brandCount: Record<string, number> = {};
    products.forEach(p => {
      brandCount[p.brand] = (brandCount[p.brand] || 0) + 1;
    });
    const brandBreakdown = Object.entries(brandCount)
      .map(([brand, count]) => ({ brand, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalProducts,
      totalSellers: uniqueSellers,
      averagePrice: Math.round(averagePrice * 100) / 100,
      categoryBreakdown,
      brandBreakdown,
    };
  },
});

/**
 * Get available marketplace types
 */
export const getMarketplaceTypes = query({
  args: {},
  returns: v.array(v.object({
    type: v.string(),
    name: v.string(),
    description: v.string(),
    icon: v.string(),
  })),
  handler: async (ctx, args) => {
    return [
      {
        type: "luxury",
        name: "Luxury Goods",
        description: "Premium luxury items and designer goods",
        icon: "Crown",
      },
      {
        type: "watches",
        name: "Watches",
        description: "Luxury timepieces and vintage watches",
        icon: "Clock",
      },
      {
        type: "jewelry",
        name: "Jewelry",
        description: "Fine jewelry, diamonds, and precious stones",
        icon: "Gem",
      },
      {
        type: "cars",
        name: "Automotive",
        description: "Luxury cars, classics, and collectible vehicles",
        icon: "Car",
      },
      {
        type: "art",
        name: "Art & Collectibles",
        description: "Fine art, sculptures, and rare collectibles",
        icon: "Palette",
      },
      {
        type: "fashion",
        name: "Fashion",
        description: "Designer clothing and haute couture",
        icon: "Shirt",
      },
      {
        type: "sneakers",
        name: "Sneakers",
        description: "Limited edition and designer sneakers",
        icon: "Footprints",
      },
      {
        type: "handbags",
        name: "Handbags",
        description: "Designer handbags and luxury accessories",
        icon: "ShoppingBag",
      },
    ];
  },
});
