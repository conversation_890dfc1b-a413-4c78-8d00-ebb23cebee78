import { v } from "convex/values";
import { action, internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import { requireAuth } from "./lib/auth_utils";

export const generateInvoiceNumber = (sellerId: string): string => {
  const timestamp = Date.now().toString().slice(-6);
  const sellerPrefix = sellerId.slice(-4).toUpperCase();
  return `HV-${sellerPrefix}-${timestamp}`;
};

export const createInvoice = mutation({
  args: {
    offlineSaleId: v.id("offlineSales"),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const offlineSale = await ctx.db.get(args.offlineSaleId);
    if (!offlineSale) {
      throw new Error("Offline sale not found");
    }

    const now = Date.now();
    const dueDate = now + (30 * 24 * 60 * 60 * 1000); // 30 days from now
    const invoiceNumber = generateInvoiceNumber(offlineSale.sellerId);

    // Get product for item description if productId exists
    let itemDescription = "Product";
    if (offlineSale.productId) {
      const product = await ctx.db.get(offlineSale.productId);
      if (product) {
        itemDescription = `${product.title} - ${product.brand}`;
      }
    }
    
    const tax = 0; // Default tax
    const totalAmount = offlineSale.salePrice + tax;

    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: offlineSale.sellerId,
      offlineSaleId: args.offlineSaleId,
      productId: offlineSale.productId, // This can now be undefined
      invoiceNumber,
      clientName: offlineSale.clientName,
      clientEmail: offlineSale.clientEmail,
      clientPhone: offlineSale.clientPhone,
      clientAddress: offlineSale.clientAddress,
      itemDescription,
      salePrice: offlineSale.salePrice,
      tax,
      totalAmount,
      paymentMethod: offlineSale.paymentMethod,
      status: "draft",
      dueDate,
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return invoiceId;
  },
});

export const createCustomInvoice = mutation({
  args: {
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Generate invoice number
    const invoiceNumber = generateInvoiceNumber(user._id);
    
    // Create a placeholder offline sale for the invoice
    // For custom invoices, we don't need a specific product
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: user._id,
      productId: undefined, // No specific product for custom invoices
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      salePrice: args.totalAmount,
      paymentMethod: "other",
      saleDate: now,
      status: "pending_payment",
      notes: args.notes,
    });

    // Create the invoice
    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: user._id,
      offlineSaleId,
      productId: undefined, // No specific product for custom invoices
      invoiceNumber,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentMethod: "other",
      status: "draft",
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return { invoiceId, offlineSaleId };
  },
});

export const saveInvoiceDraft = mutation({
  args: {
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Generate invoice number
    const invoiceNumber = generateInvoiceNumber(user._id);
    
    // Create a placeholder offline sale for the draft invoice
    // For custom invoices, we don't need a specific product
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: user._id,
      productId: undefined, // No specific product for custom invoices
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      salePrice: args.totalAmount,
      paymentMethod: "other",
      saleDate: now,
      status: "pending_payment",
      notes: args.notes,
    });

    // Create the draft invoice
    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: user._id,
      offlineSaleId,
      productId: undefined, // No specific product for custom invoices
      invoiceNumber,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentMethod: "other",
      status: "draft", // Explicitly set as draft
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return { invoiceId, offlineSaleId };
  },
});

export const updateInvoiceDraft = mutation({
  args: {
    invoiceId: v.id("invoices"),
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not editable");
    }

    // Update the invoice
    await ctx.db.patch(args.invoiceId, {
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)),
      updatedAt: now,
    });

    // Also update the offline sale
    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    if (offlineSale) {
      await ctx.db.patch(offlineSale._id, {
        clientName: args.clientName,
        clientEmail: args.clientEmail,
        clientPhone: args.clientPhone,
        clientAddress: args.clientAddress,
        salePrice: args.totalAmount,
        notes: args.notes,
      });
    }

    return args.invoiceId;
  },
});

export const getInvoices = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_sellerId", (q: any) => q.eq("sellerId", user._id))
      .order("desc")
      .collect();

    // Get related data for each invoice
    const invoicesWithDetails = await Promise.all(
      invoices.map(async (invoice) => {
        const offlineSale = await ctx.db.get(invoice.offlineSaleId);
        const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
        
        return {
          ...invoice,
          offlineSale,
          product,
        };
      })
    );

    return invoicesWithDetails;
  },
});

export const getInvoice = query({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return null;
    }

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);
    
    // Get seller's company information for branding
    const sellerProfile = seller ? await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", seller._id))
      .first() : null;

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
      sellerProfile,
    };
  },
});

export const updateInvoiceStatus = mutation({
  args: {
    invoiceId: v.id("invoices"),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Update invoice status
    await ctx.db.patch(args.invoiceId, {
      status: args.status,
      updatedAt: now,
    });

    // If marking as paid, also update related entities
    if (args.status === "paid") {
      // Get the invoice to find related entities
      const invoice = await ctx.db.get(args.invoiceId);
      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Update offline sale status to paid
      if (invoice.offlineSaleId) {
        try {
          await ctx.db.patch(invoice.offlineSaleId, {
            status: "paid",
          });
        } catch (error) {
          console.error("Failed to update offline sale status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update product status to sold if it exists
      if (invoice.productId) {
        try {
          await ctx.db.patch(invoice.productId, {
            status: "sold",
            updatedAt: now,
          });
        } catch (error) {
          console.error("Failed to update product status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update invoice with paid date
      await ctx.db.patch(args.invoiceId, {
        paidDate: now,
        updatedAt: now,
      });

      // Log the payment event
      try {
        await ctx.db.insert("analytics", {
          eventType: "invoice_paid",
          userId: invoice.sellerId,
          sellerId: invoice.sellerId,
          productId: invoice.productId,
          timestamp: now,
          metadata: {
            source: args.invoiceId,
            category: "sales",
            revenue: invoice.totalAmount,
            paymentMethod: invoice.paymentMethod,
          },
        });
      } catch (error) {
        console.error("Failed to log payment event:", error);
        // Don't fail the main operation if logging fails
      }
    }
  },
});

export const bulkUpdateInvoiceStatus = mutation({
  args: {
    invoiceIds: v.array(v.id("invoices")),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results: { invoiceId: Id<"invoices">; success: boolean; error?: string }[] = [];

    for (const invoiceId of args.invoiceIds) {
      try {
        // Update invoice status
        await ctx.db.patch(invoiceId, {
          status: args.status,
          updatedAt: now,
        });

        // If marking as paid, also update related entities
        if (args.status === "paid") {
          const invoice = await ctx.db.get(invoiceId);
          if (invoice) {
            // Update offline sale status to paid
            if (invoice.offlineSaleId) {
              try {
                await ctx.db.patch(invoice.offlineSaleId, {
                  status: "paid",
                });
              } catch (error) {
                console.error(`Failed to update offline sale ${invoice.offlineSaleId}:`, error);
              }
            }

            // Update product status to sold if it exists
            if (invoice.productId) {
              try {
                await ctx.db.patch(invoice.productId, {
                  status: "sold",
                  updatedAt: now,
                });
              } catch (error) {
                console.error(`Failed to update product ${invoice.productId}:`, error);
              }
            }

            // Update invoice with paid date
            await ctx.db.patch(invoiceId, {
              paidDate: now,
              updatedAt: now,
            });

            // Log the payment event
            try {
              await ctx.db.insert("analytics", {
                eventType: "invoice_paid",
                userId: invoice.sellerId,
                sellerId: invoice.sellerId,
                productId: invoice.productId,
                timestamp: now,
                metadata: {
                  source: invoiceId,
                  category: "sales",
                  revenue: invoice.totalAmount,
                  paymentMethod: invoice.paymentMethod,
                },
              });
            } catch (error) {
              console.error("Failed to log payment event:", error);
            }
          }
        }

        results.push({ invoiceId, success: true });
      } catch (error) {
        console.error(`Failed to update invoice ${invoiceId}:`, error);
        results.push({ invoiceId, success: false, error: error instanceof Error ? error.message : String(error) });
      }
    }

    return results;
  },
});

export const markInvoiceAsPaid = mutation({
  args: {
    invoiceId: v.id("invoices"),
    paymentMethod: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Update invoice status to paid
    await ctx.db.patch(args.invoiceId, {
      status: "paid",
      paidDate: now,
      updatedAt: now,
      notes: args.notes ? `${invoice.notes || ""}\n\nPayment received: ${args.notes}`.trim() : invoice.notes,
    });

    // Update offline sale status to paid
    if (invoice.offlineSaleId) {
      try {
        await ctx.db.patch(invoice.offlineSaleId, {
          status: "paid",
        });
      } catch (error) {
        console.error("Failed to update offline sale status:", error);
      }
    }

    // Update product status to sold if it exists
    if (invoice.productId) {
      try {
        await ctx.db.patch(invoice.productId, {
          status: "sold",
          updatedAt: now,
        });
      } catch (error) {
        console.error("Failed to update product status:", error);
      }
    }

    // Log the payment event
    try {
      await ctx.db.insert("analytics", {
        eventType: "invoice_paid",
        userId: invoice.sellerId,
        sellerId: invoice.sellerId,
        productId: invoice.productId,
        timestamp: now,
        metadata: {
          source: args.invoiceId,
          category: "sales",
          revenue: invoice.totalAmount,
          paymentMethod: args.paymentMethod || invoice.paymentMethod,
        },
      });
    } catch (error) {
      console.error("Failed to log payment event:", error);
    }

    return { success: true, invoiceId: args.invoiceId };
  },
});

export const convertDraftToInvoice = mutation({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not a draft");
    }

    // Update the invoice status from draft to sent
    await ctx.db.patch(args.invoiceId, {
      status: "sent",
      sentAt: now,
      updatedAt: now,
    });

    return args.invoiceId;
  },
});

export const generateInvoicePDF = action({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args): Promise<Id<"_storage">> => {
    try {
      // Get invoice data
      const invoiceData = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
        invoiceId: args.invoiceId,
      });

      if (!invoiceData) {
        throw new Error("Invoice not found");
      }

      console.log("Preparing invoice data for PDF generation:", invoiceData.invoiceNumber);

      // For now, we'll store the invoice data as JSON that the frontend can use
      // to generate the PDF. This avoids the jsPDF issues in Node.js environment.
      const invoiceDataBlob = new Blob([JSON.stringify(invoiceData)], { 
        type: "application/json" 
      });
      
      // Store invoice data in Convex storage
      const storageId = await ctx.storage.store(
        invoiceDataBlob,
        { sha256: `invoice-data-${invoiceData.invoiceNumber}.json` }
      );

      console.log("Invoice data stored in storage:", storageId);

      // Update invoice with PDF storage ID (we'll use this to store the actual PDF later)
      await ctx.runMutation(internal.invoices.updateInvoicePDF, {
        invoiceId: args.invoiceId,
        pdfStorageId: storageId,
      });

      console.log("Invoice updated with storage ID");

      return storageId;
    } catch (error) {
      console.error("Error in generateInvoicePDF:", error);
      throw error;
    }
  },
});

export const generatePDFInternal = internalAction({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args): Promise<Id<"_storage">> => {
    try {
      // Get invoice data
      const invoiceData = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
        invoiceId: args.invoiceId,
      });

      if (!invoiceData) {
        throw new Error("Invoice not found");
      }

      console.log("Generating PDF for invoice:", invoiceData.invoiceNumber);

      // Generate PDF using jsPDF
      const pdfBuffer = await generatePDFBuffer(invoiceData);
      
      console.log("PDF generated successfully, size:", pdfBuffer.byteLength);
      
      // Store PDF in Convex storage
      const storageId = await ctx.storage.store(
        new Blob([pdfBuffer], { type: "application/pdf" }),
        { sha256: `invoice-${invoiceData.invoiceNumber}.pdf` }
      );

      console.log("PDF stored in storage:", storageId);

      // Update invoice with PDF storage ID
      await ctx.runMutation(internal.invoices.updateInvoicePDF, {
        invoiceId: args.invoiceId,
        pdfStorageId: storageId,
      });

      console.log("Invoice updated with PDF storage ID");

      return storageId;
    } catch (error) {
      console.error("Error in generatePDFInternal:", error);
      throw error;
    }
  },
});

export const getInvoiceForPDF = internalQuery({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) return null;

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);
    
    // Get seller's company information for branding
    const sellerProfile = seller ? await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", seller._id))
      .first() : null;

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
      sellerProfile,
    };
  },
});

export const updateInvoicePDF = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    pdfStorageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.invoiceId, {
      pdfStorageId: args.pdfStorageId,
      updatedAt: Date.now(),
    });
  },
});

export const sendInvoiceEmail = action({
  args: {
    invoiceId: v.id("invoices"),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
      invoiceId: args.invoiceId,
    });

    if (!invoice || !invoice.offlineSale) {
      throw new Error("Invoice or sale not found");
    }

    // Generate PDF if not exists
    let pdfStorageId = invoice.pdfStorageId;
    if (!pdfStorageId) {
      pdfStorageId = await ctx.runAction(internal.invoices.generatePDFInternal, {
        invoiceId: args.invoiceId,
      });
    }

    // Get PDF URL for attachment
    const pdfUrl = await ctx.storage.getUrl(pdfStorageId);
    
    // Send email with invoice
    await ctx.runAction(internal.invoices.sendInvoiceEmailInternal, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
      recipientName: invoice.offlineSale.clientName,
      pdfUrl: pdfUrl!,
      customMessage: args.customMessage,
    });

    // Update invoice status and email log
    await ctx.runMutation(internal.invoices.logEmailSent, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
    });
  },
});

export const sendInvoiceEmailInternal = internalAction({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
    recipientName: v.string(),
    pdfUrl: v.string(),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // This would integrate with your email service
    // For now, we'll use the existing sendEmail function
    const { sendEmail } = await import("./lib/email");
    
    await sendEmail(ctx, {
      to: args.recipientEmail,
      subject: `Invoice from HauteVault - ${args.invoiceId}`,
      react: {
        // We'll create an invoice email template
        type: "invoice",
        data: {
          recipientName: args.recipientName,
          invoiceId: args.invoiceId,
          pdfUrl: args.pdfUrl,
          customMessage: args.customMessage,
        },
      },
    });
  },
});

export const logEmailSent = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const emailLogString = `${new Date().toISOString()} - Sent to ${args.recipientEmail}`;

    await ctx.db.patch(args.invoiceId, {
      emailsSent: [...invoice.emailsSent, emailLogString],
      status: invoice.status === "draft" ? "sent" : invoice.status,
      updatedAt: Date.now(),
    });
  },
});

export const getStorageUrl = action({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args): Promise<string | null> => {
    try {
      const url = await ctx.storage.getUrl(args.storageId);
      return url;
    } catch (error) {
      console.error("Error getting storage URL:", error);
      return null;
    }
  },
});

export const deleteInvoiceDraft = mutation({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not a draft");
    }

    // Delete the draft invoice
    await ctx.db.delete(args.invoiceId);
    
    // Also delete the associated offline sale if it exists
    if (invoice.offlineSaleId) {
      await ctx.db.delete(invoice.offlineSaleId);
    }

    return args.invoiceId;
  },
});

export const storeGeneratedPDF = action({
  args: {
    invoiceId: v.id("invoices"),
    pdfData: v.string(), // Changed from v.bytes() to v.string() for base64
  },
  handler: async (ctx, args): Promise<Id<"_storage">> => {
    try {
      // Get invoice data to get the invoice number
      const invoiceData = await ctx.runQuery(internal.invoices.getInvoiceForPDF, {
        invoiceId: args.invoiceId,
      });

      if (!invoiceData) {
        throw new Error("Invoice not found");
      }

      console.log("Storing generated PDF for invoice:", invoiceData.invoiceNumber);

      // Convert base64 string to Uint8Array for storage
      const binaryString = atob(args.pdfData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Store the actual PDF in Convex storage
      const storageId = await ctx.storage.store(
        new Blob([bytes], { type: "application/pdf" }),
        { sha256: `invoice-${invoiceData.invoiceNumber}.pdf` }
      );

      console.log("PDF stored in storage:", storageId);

      // Update invoice with the actual PDF storage ID
      await ctx.runMutation(internal.invoices.updateInvoicePDF, {
        invoiceId: args.invoiceId,
        pdfStorageId: storageId,
      });

      console.log("Invoice updated with actual PDF storage ID");

      return storageId;
    } catch (error) {
      console.error("Error in storeGeneratedPDF:", error);
      throw error;
    }
  },
});

// Helper function to generate PDF using jsPDF
async function generatePDFBuffer(invoiceData: any): Promise<ArrayBuffer> {
  // Import jsPDF dynamically since it's a Node.js module
  const { jsPDF } = await import("jspdf");
  
  const doc = new jsPDF();
  
  // Set font and colors
  doc.setFont("helvetica");
  doc.setTextColor(48, 41, 35); // Primary dark color
  
  // Header with seller company information
  let headerY = 30;
  
  // Company logo placeholder (if we have one, we'd need to load and embed it)
  // For now, we'll add company name and info
  
  if (invoiceData.sellerProfile?.businessName) {
    doc.setFontSize(20);
    doc.text(invoiceData.sellerProfile.businessName.toUpperCase(), 20, headerY);
    headerY += 10;
  } else {
    doc.setFontSize(20);
    doc.text("HAUTEVAULT", 20, headerY);
    headerY += 8;
    doc.setFontSize(12);
    doc.setTextColor(151, 148, 135); // Secondary sage color
    doc.text("Luxury Marketplace", 20, headerY);
    headerY += 10;
  }
  
  // Company contact information
  doc.setFontSize(10);
  doc.setTextColor(151, 148, 135);
  
  if (invoiceData.sellerProfile) {
    // Company address
    if (invoiceData.sellerProfile.address) {
      doc.text(invoiceData.sellerProfile.address.street, 20, headerY);
      headerY += 5;
      doc.text(`${invoiceData.sellerProfile.address.city}, ${invoiceData.sellerProfile.address.state} ${invoiceData.sellerProfile.address.zipCode}`, 20, headerY);
      headerY += 5;
      doc.text(invoiceData.sellerProfile.address.country, 20, headerY);
      headerY += 5;
    }
    
    // Company contact info
    if (invoiceData.sellerProfile.companyPhone || invoiceData.sellerProfile.phone) {
      doc.text(`Phone: ${invoiceData.sellerProfile.companyPhone || invoiceData.sellerProfile.phone}`, 20, headerY);
      headerY += 5;
    }
    
    if (invoiceData.sellerProfile.companyEmail) {
      doc.text(`Email: ${invoiceData.sellerProfile.companyEmail}`, 20, headerY);
      headerY += 5;
    }
    
    if (invoiceData.sellerProfile.website) {
      doc.text(`Website: ${invoiceData.sellerProfile.website}`, 20, headerY);
      headerY += 5;
    }
  }
  
  // Reset position for invoice details
  headerY += 10;
  
  // Invoice details
  doc.setFontSize(16);
  doc.setTextColor(48, 41, 35);
  doc.text(`Invoice #: ${invoiceData.invoiceNumber}`, 20, headerY);
  headerY += 15;
  
  doc.setFontSize(12);
  doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, headerY);
  headerY += 10;
  doc.text(`Due Date: ${invoiceData.dueDate ? new Date(invoiceData.dueDate).toLocaleDateString() : 'Not set'}`, 20, headerY);
  headerY += 10;
  doc.text(`Payment Terms: ${invoiceData.paymentTerms || 'Net 30'}`, 20, headerY);
  headerY += 20;
  
  // Client information
  doc.setFontSize(14);
  doc.setTextColor(48, 41, 35);
  doc.text("Bill To:", 20, headerY);
  headerY += 15;
  
  doc.setFontSize(12);
  doc.setTextColor(151, 148, 135);
  doc.text(invoiceData.clientName, 20, headerY);
  headerY += 10;
  doc.text(invoiceData.clientEmail, 20, headerY);
  headerY += 10;
  if (invoiceData.clientPhone) {
    doc.text(invoiceData.clientPhone, 20, headerY);
    headerY += 10;
  }
  doc.text(invoiceData.clientAddress.street, 20, headerY);
  headerY += 10;
  doc.text(`${invoiceData.clientAddress.city}, ${invoiceData.clientAddress.state} ${invoiceData.clientAddress.zipCode}`, 20, headerY);
  headerY += 10;
  doc.text(invoiceData.clientAddress.country, 20, headerY);
  headerY += 20;
  
  // Items table
  doc.setFontSize(14);
  doc.setTextColor(48, 41, 35);
  doc.text("Items:", 20, headerY);
  headerY += 15;
  
  let yPosition = headerY;
  let total = 0;
  
  // Table headers
  doc.setFontSize(10);
  doc.setTextColor(151, 148, 135);
  doc.text("Description", 20, yPosition);
  doc.text("Qty", 100, yPosition);
  doc.text("Unit Price", 130, yPosition);
  doc.text("Amount", 170, yPosition);
  
  yPosition += 10;
  
  // Table rows
  doc.setFontSize(10);
  doc.setTextColor(48, 41, 35);
  
  if (invoiceData.items && Array.isArray(invoiceData.items)) {
    for (const item of invoiceData.items) {
      if (yPosition > 250) {
        doc.addPage();
        yPosition = 20;
      }
      
      doc.text(item.description || "Item", 20, yPosition);
      doc.text(item.quantity.toString(), 100, yPosition);
      doc.text(`$${item.unitPrice.toFixed(2)}`, 130, yPosition);
      doc.text(`$${item.amount.toFixed(2)}`, 170, yPosition);
      
      total += item.amount;
      yPosition += 8;
    }
  } else {
    // Fallback for single item
    doc.text(invoiceData.itemDescription || "Product/Service", 20, yPosition);
    doc.text("1", 100, yPosition);
    doc.text(`$${invoiceData.salePrice.toFixed(2)}`, 130, yPosition);
    doc.text(`$${invoiceData.salePrice.toFixed(2)}`, 170, yPosition);
    total = invoiceData.salePrice;
    yPosition += 8;
  }
  
  // Totals
  yPosition += 10;
  doc.setFontSize(12);
  doc.setTextColor(151, 148, 135);
  doc.text("Subtotal:", 150, yPosition);
  doc.text(`$${total.toFixed(2)}`, 170, yPosition);
  
  yPosition += 8;
  const tax = invoiceData.tax || 0;
  doc.text("Tax:", 150, yPosition);
  doc.text(`$${tax.toFixed(2)}`, 170, yPosition);
  
  yPosition += 8;
  doc.setFontSize(14);
  doc.setTextColor(48, 41, 35);
  doc.text("Total:", 150, yPosition);
  doc.text(`$${(total + tax).toFixed(2)}`, 170, yPosition);
  
  // Notes
  if (invoiceData.notes) {
    yPosition += 20;
    doc.setFontSize(12);
    doc.setTextColor(151, 148, 135);
    doc.text("Notes:", 20, yPosition);
    yPosition += 8;
    doc.setFontSize(10);
    doc.setTextColor(48, 41, 35);
    
    // Split notes into lines if too long
    const words = invoiceData.notes.split(' ');
    let line = '';
    for (const word of words) {
      const testLine = line + word + ' ';
      if (doc.getTextWidth(testLine) > 150) {
        doc.text(line, 20, yPosition);
        yPosition += 6;
        line = word + ' ';
      } else {
        line = testLine;
      }
    }
    if (line) {
      doc.text(line, 20, yPosition);
    }
  }
  
  // Footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.setTextColor(151, 148, 135);
    doc.text(`Page ${i} of ${pageCount}`, 20, 280);
    doc.text("Thank you for your business!", 150, 280);
  }
  
  return doc.output('arraybuffer');
}