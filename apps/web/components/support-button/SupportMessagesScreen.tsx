"use client";

import { <PERSON><PERSON>ircle, ArrowRight, ChevronRight, X, FileText, User } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { SupportScreen, SupportItem } from "./types";
import { SupportBottomNavigation } from "./SupportBottomNavigation";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import React, { useState } from "react";
import { ConversationView } from "../seller/messages/ConversationView";

interface SupportMessagesScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  onOpenConversation: (conversation: any) => void;
  unreadCount?: number; 
}

export function SupportMessagesScreen({ 
  onClose, 
  onNavigate, 
  onOpenConversation,
  unreadCount
}: SupportMessagesScreenProps) {
  const [selectedConversation, setSelectedConversation] = useState<any>(null);
  
  // Fetch both support tickets and regular user messages
  const supportItems = useQuery(api.supportTickets.getUserSupportItems, {});
  const userConversations = useQuery(api.messages.getConversations);

  // Combine and sort all items by most recent activity
  const allItems = React.useMemo(() => {
    const items: Array<{
      id: string;
      type: 'support' | 'message';
      data: any;
      lastActivity: number;
      unreadCount: number;
    }> = [];

    // Add support tickets
    if (supportItems) {
      supportItems.forEach((item: any) => {
        items.push({
          id: item._id,
          type: 'support',
          data: item,
          lastActivity: item.lastMessageAt,
          unreadCount: 0, // Support tickets don't have unread counts in the same way
        });
      });
    }

    // Add user conversations
    if (userConversations) {
      userConversations.forEach((conversation: any) => {
        items.push({
          id: conversation._id,
          type: 'message',
          data: conversation,
          lastActivity: conversation.lastMessageAt,
          unreadCount: conversation.unreadCount || 0,
        });
      });
    }

    // Sort by most recent activity (newest first)
    return items.sort((a: any, b: any) => b.lastActivity - a.lastActivity);
  }, [supportItems, userConversations]);

  const renderSupportItem = (item: SupportItem) => {
    return (
      <div 
        key={item._id}
        className="p-4 hover:bg-muted/50 cursor-pointer flex items-center justify-between"
        onClick={() => onOpenConversation({ type: "ticket", ticket: item.ticket })}
      >
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex -space-x-1">
            <div className="w-8 h-8 bg-primary/10 rounded-full border border-background overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <FileText className="w-4 h-4 text-primary" />
              </div>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium text-foreground mb-1 truncate">
              {item.subject}
            </div>
            <div className="text-sm text-muted-foreground">
              Ticket #{item.ticketNumber} • {item.status} • {formatDistanceToNow(item.lastMessageAt, { addSuffix: true })}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <ChevronRight className="w-4 h-4 text-muted-foreground flex-shrink-0" />
        </div>
      </div>
    );
  };

  const renderUserMessage = (item: any) => {
    const conversation = item.data;
    const otherParticipant = conversation.otherParticipant;
    const product = conversation.product;
    
    return (
      <div 
        key={item.id}
        className="p-4 hover:bg-muted/50 cursor-pointer flex items-center justify-between"
        onClick={() => setSelectedConversation(conversation)}
      >
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex -space-x-1">
            <div className="w-8 h-8 bg-accent/10 rounded-full border border-background overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <User className="w-4 h-4 text-accent" />
              </div>
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium text-foreground mb-1 truncate">
              {otherParticipant?.name || otherParticipant?.email || 'Unknown User'}
            </div>
            <div className="text-sm text-muted-foreground truncate">
              {conversation.lastMessagePreview}
              {product && ` • ${product.name}`}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {formatDistanceToNow(conversation.lastMessageAt, { addSuffix: true })}
              {item.unreadCount > 0 && (
                <span className="ml-2 bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">
                  {item.unreadCount}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <ChevronRight className="w-4 h-4 text-muted-foreground flex-shrink-0" />
        </div>
      </div>
    );
  };

  const renderItem = (item: any) => {
    if (item.type === 'support') {
      return renderSupportItem(item.data);
    } else {
      return renderUserMessage(item);
    }
  };

  // If a conversation is selected, show the conversation view
  if (selectedConversation) {
    return (
      <ConversationView
        conversation={selectedConversation}
        onBack={() => setSelectedConversation(null)}
        isSupportMode={true}
      />
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-background px-4 py-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Messages</h3>
          <button 
            onClick={onClose}
            className="p-1 hover:bg-muted rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-muted-foreground" />
          </button>
        </div>
      </div>
      
      {/* Messages List */}
      <div className="flex-1 bg-background overflow-y-auto">
        {allItems && allItems.length > 0 ? (
          <div className="divide-y divide-border">
            {allItems.map(renderItem)}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <MessageCircle className="w-12 h-12 text-muted-foreground/50 mb-3" />
            <h4 className="text-lg font-medium text-foreground mb-2">No messages yet</h4>
            <p className="text-sm text-muted-foreground font-light mb-4">
              Start a conversation or contact support
            </p>
          </div>
        )}
      </div>

      {/* Send Message Button */}
      <div className="pb-4 px-8 bg-background">
        <button 
          onClick={() => onNavigate("compose")}
          className="w-full bg-primary text-primary-foreground py-3 px-4 rounded-2xl hover:bg-primary/90 transition-colors font-medium flex items-center justify-center"
        >
          Send us a message
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      </div>
      
      {/* Bottom Navigation */}
      <SupportBottomNavigation
        activeScreen="messages"
        onNavigate={onNavigate}
        unreadCount={unreadCount}
      />
    </div>
  );
}
