"use client";

import React, { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { authClient } from "@repo/backend/better-auth/client";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";
import { X, Apple, Eye, EyeOff } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";


const authSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().optional(),
  name: z.string().optional(),
  lastName: z.string().optional(),
  userType: z.enum(["consumer", "seller"]).optional(),
  // Seller application fields (optional for consumers)
  company: z.string().optional(),
  productSpecialty: z.string().optional(),
  numberOfProducts: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),
  referral: z.string().optional(),
}).refine((data) => {
  // For signup, require name and confirmPassword
  if (data.confirmPassword !== undefined) {
    return data.name && data.name.trim().length >= 2;
  }
  return true;
}, {
  message: "Name is required for signup",
  path: ["name"]
}).refine((data) => {
  // For signup, ensure passwords match
  if (data.confirmPassword !== undefined) {
    return data.password === data.confirmPassword;
  }
  return true;
}, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
}).refine((data) => {
  // If user type is seller and this is signup, require seller application fields
  if (data.userType === "seller" && data.confirmPassword !== undefined) {
    return data.company && data.productSpecialty && data.numberOfProducts && data.phone && data.lastName;
  }
  return true;
}, {
  message: "Please fill in all required seller application fields",
  path: ["company", "productSpecialty", "numberOfProducts", "phone", "lastName"]
}).refine((data) => {
  // If user type is seller and this is signup, validate phone number format
  if (data.userType === "seller" && data.confirmPassword !== undefined && data.phone) {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(data.phone);
  }
  return true;
}, {
  message: "Please enter a valid phone number",
  path: ["phone"]
});

type AuthFormData = z.infer<typeof authSchema>;

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: "login" | "signup";
  defaultUserType?: "consumer" | "seller";
  social?: boolean;
}

export function AuthModal({ isOpen, onClose, defaultMode = "login", defaultUserType = "consumer", social = true }: AuthModalProps) {
  const [mode, setMode] = useState<"login" | "signup">(defaultMode);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [signupStep, setSignupStep] = useState<"creating" | "updating" | "submitting" | "complete">("creating");
  const updateUserProfile = useMutation(api.auth.updateUserProfile);
  const submitSellerApplication = useMutation(api.sellerApplicationsSimple.submitApplication);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<AuthFormData>({
    resolver: zodResolver(authSchema),
    defaultValues: {
      userType: defaultUserType,
    },
  });

  // Always ensure userType is set, even for login
  React.useEffect(() => {
    if (!watch("userType")) {
      setValue("userType", defaultUserType);
    }
    console.log("Form state updated:", {
      userType: watch("userType"),
      mode,
      defaultUserType
    });
  }, [watch, setValue, defaultUserType, mode]);

  // Update mode when defaultMode prop changes
  React.useEffect(() => {
    setMode(defaultMode);
  }, [defaultMode]);

  // Reset form when mode or defaultUserType changes
  React.useEffect(() => {
    reset();
    if (mode === "signup") {
      setValue("userType", defaultUserType);
      setValue("name", "");
      setValue("lastName", "");
      setValue("phone", "");
      setValue("company", "");
      setValue("productSpecialty", "");
      setValue("numberOfProducts", "");
      setValue("website", "");
      setValue("referral", "");
    }
    setShowPassword(false);
    setShowConfirmPassword(false);
    setSignupStep("creating");
  }, [mode, defaultUserType, reset, setValue]);

  const userType = watch("userType");

  const onSubmit = async (data: AuthFormData) => {
    console.log("=== FORM SUBMISSION START ===");
    console.log("Form submitted with data:", data);
    console.log("Form mode:", mode);
    console.log("Form errors:", errors);
    console.log("Form values:", watch());
    console.log("Form validation state:", Object.keys(errors).length === 0 ? "VALID" : "INVALID");
    
    setIsLoading(true);
    setSignupStep("creating");

    try {
      if (mode === "signup") {
        // Ensure required fields are present for signup
        if (!data.name || !data.confirmPassword) {
          toast.error("Missing required fields", {
            description: "Please fill in all required fields.",
          });
          return;
        }

        const { data: authData, error } = await authClient.signUp.email({
          name: data.name,
          email: data.email,
          password: data.password,
          callbackURL: "/",
        });

        if (error) {
          toast.error("Sign up failed", {
            description: error.message || "Please check your information and try again.",
          });
          return;
        }

        if (authData) {
          // For seller accounts, wait for authentication then submit application
          if (data.userType === "seller") {
            try {
              // Wait for the user to be properly authenticated (give it time)
              setSignupStep("creating");
              await new Promise(resolve => setTimeout(resolve, 3000));
              
              // Validate required fields
              if (!data.company || !data.productSpecialty || !data.numberOfProducts || !data.phone || !data.lastName) {
                console.error("Missing required fields:", {
                  company: data.company,
                  productSpecialty: data.productSpecialty,
                  numberOfProducts: data.numberOfProducts,
                  phone: data.phone,
                  lastName: data.lastName
                });
                throw new Error("Missing required seller application fields");
              }
              
              // Update user type first
              setSignupStep("updating");
              try {
                await updateUserProfile({
                  userType: data.userType,
                });
                console.log("User type updated to seller");
              } catch (profileError) {
                console.error("Failed to update user type:", profileError);
                // Continue with application submission even if profile update fails
              }
              
              // Submit the seller application
              setSignupStep("submitting");
              const applicationData = {
                firstName: data.name || '',
                lastName: data.lastName || '',
                email: data.email,
                phone: data.phone,
                dateOfBirth: "N/A", // Required field but not collected in form
                businessName: data.company,
                businessType: "individual" as const,
                taxId: "N/A", // Not collected in form
                businessAddress: "N/A", // Not collected in form
                businessCity: "N/A", // Not collected in form
                businessState: "N/A", // Not collected in form
                businessZip: "N/A", // Not collected in form
                businessCountry: "N/A", // Not collected in form
                yearsExperience: "1", // Default value
                previousPlatforms: [],
                monthlyVolume: data.numberOfProducts,
                specialties: data.productSpecialty ? [data.productSpecialty] : [],
                termsAccepted: true,
                privacyAccepted: true,
              };

              console.log("Submitting seller application with data:", applicationData);
              const applicationId = await submitSellerApplication(applicationData);
              console.log("Seller application submitted successfully, ID:", applicationId);
              
              if (applicationId) {
                setSignupStep("complete");
                toast.success("Account created successfully!", {
                  description: "Welcome to HauteVault! Your seller application has been submitted and is under review.",
                });
                onClose();
                
                // Redirect to seller dashboard
                window.location.href = "/seller/dashboard";
              } else {
                throw new Error("Failed to submit application - no ID returned");
              }
            } catch (appError) {
              console.error("Failed to submit seller application:", appError);
              console.error("Application data that failed:", {
                name: data.name,
                lastName: data.lastName,
                company: data.company,
                productSpecialty: data.productSpecialty,
                numberOfProducts: data.numberOfProducts,
                email: data.email,
                phone: data.phone
              });
              
              // Store the data temporarily as fallback
              const sellerApplicationData = {
                firstName: data.name || '',
                lastName: data.lastName || '',
                email: data.email,
                phone: data.phone,
                company: data.company,
                productSpecialty: data.productSpecialty,
                numberOfProducts: data.numberOfProducts,
                website: data.website,
                referral: data.referral,
                timestamp: Date.now(),
              };
              
              sessionStorage.setItem('pendingSellerApplication', JSON.stringify(sellerApplicationData));
              
              toast.success("Account created successfully!", {
                description: "Welcome to HauteVault! Please complete your seller application in your dashboard.",
              });
              onClose();
              
              // Redirect to seller dashboard where they can complete their application
              window.location.href = "/seller/dashboard";
            }
          } else {
            // For consumer accounts, redirect to subscription page
            toast.success("Account created successfully!", {
              description: "Welcome to HauteVault!",
            });
            onClose();
            window.location.href = "/subscribe";
          }
        }
      } else {
        const { data: authData, error } = await authClient.signIn.email({
          email: data.email,
          password: data.password,
          callbackURL: "/",
        });

        if (error) {
          toast.error("Sign in failed", {
            description: error.message || "Please check your credentials and try again.",
          });
          return;
        }

        if (authData) {
          toast.success("Welcome back!", {
            description: "You have been successfully signed in.",
          });
          onClose();
        }
      }
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error("Something went wrong", {
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider: "google" | "apple") => {
    setIsLoading(true);
    try {
      await authClient.signIn.social({
        provider: provider,
        callbackURL: "/",
      });
    } catch {
      toast.error("Social login failed", {
        description: "Please try again or use email/password.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/80 backdrop-blur-sm" onClick={onClose} />
      <Card className="relative min-w-md max-w-2xl mx-4">
        <CardHeader className="relative">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-0 text-zinc-400 hover:text-white"
            onClick={onClose}
          >
            <X className="w-4 h-4" />
          </Button>
          <CardTitle className="text-xl">
            {mode === "login" ? "Welcome back" : "Join HauteVault"}
          </CardTitle>
          <CardDescription className="text-zinc-400">
            {mode === "login" 
              ? "Sign in to access your account" 
              : "Create your account to get started"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Social Login Buttons */}
          {social && (
            <>
              <div className="space-y-3">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSocialLogin("google")}
                  disabled={isLoading}
                >
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                    <path
                      d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                      fill="currentColor"
                    />
                  </svg>
                  Continue with Google
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSocialLogin("apple")}
                  disabled={isLoading}
                >
                  <Apple className="w-4 h-4 mr-2" />
                  Continue with Apple
                </Button>
              </div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-border" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background px-2 text-muted">Or continue with</span>
                </div>
              </div>
            </>
          )}

          {mode === "signup" && userType === "seller" ? (
            // Show combined registration + seller application form
            <div className="space-y-4">
              <div className="text-center mb-4">
                <h3 className="text-lg font-medium text-foreground">Seller Application</h3>
                <p className="text-sm text-muted-foreground">
                  Create your account and submit your seller application
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  * Required fields
                </p>
                
                {/* Progress indicator for seller signup */}
                {isLoading && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <div className="text-sm font-medium text-foreground mb-2">
                      {signupStep === "creating" && "Creating your account..."}
                      {signupStep === "updating" && "Updating user profile..."}
                      {signupStep === "submitting" && "Submitting seller application..."}
                      {signupStep === "complete" && "Setup complete!"}
                    </div>
                    <div className="w-full bg-background rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-500 ${
                          signupStep === "creating" ? "w-1/4 bg-primary" :
                          signupStep === "updating" ? "w-2/4 bg-primary" :
                          signupStep === "submitting" ? "w-3/4 bg-primary" :
                          signupStep === "complete" ? "w-full bg-green-500" : "w-0"
                        }`}
                      />
                    </div>
                  </div>
                )}
              </div>
              
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Account Registration Fields */}
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register("email")}
                    className={cn(
                      "placeholder:text-muted",
                      errors.email && "border-red-500"
                    )}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-400">{errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="********"
                      {...register("password")}
                      className={cn(
                        "placeholder:text-muted pr-10",
                        errors.password && "border-red-500"
                      )}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-400">{errors.password.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="********"
                      {...register("confirmPassword")}
                      className={cn(
                        "placeholder:text-muted pr-10",
                        errors.confirmPassword && "border-red-500"
                      )}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-400">{errors.confirmPassword.message}</p>
                  )}
                </div>

                {/* Seller Application Fields */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      {...register("name")}
                      className={cn(
                        "placeholder:text-muted",
                        errors.name && "border-red-500"
                      )}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-400">{errors.name.message}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      {...register("lastName")}
                      className="placeholder:text-muted"
                    />
                    {errors.lastName && (
                      <p className="text-sm text-red-400">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company Name *</Label>
                  <Input
                    id="company"
                    placeholder="Your Company"
                    {...register("company")}
                    className={cn(
                      "placeholder:text-muted",
                      errors.company && "border-red-500"
                    )}
                  />
                  {errors.company && (
                    <p className="text-sm text-red-400">{errors.company.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="productSpecialty">Product Specialty *</Label>
                  <Input
                    id="productSpecialty"
                    placeholder="e.g., Luxury watches, Designer bags"
                    {...register("productSpecialty")}
                    className={cn(
                      "placeholder:text-muted",
                      errors.productSpecialty && "border-red-500"
                    )}
                  />
                  {errors.productSpecialty && (
                    <p className="text-sm text-red-400">{errors.productSpecialty.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="numberOfProducts">Number of Products *</Label>
                  <Select onValueChange={(value) => setValue("numberOfProducts", value)}>
                    <SelectTrigger className={cn(
                      "placeholder:text-muted",
                      errors.numberOfProducts && "border-red-500"
                    )}>
                      <SelectValue placeholder="Select number of products" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1-10">1-10 products</SelectItem>
                      <SelectItem value="11-50">11-50 products</SelectItem>
                      <SelectItem value="51-100">51-100 products</SelectItem>
                      <SelectItem value="100+">100+ products</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.numberOfProducts && (
                    <p className="text-sm text-red-400">{errors.numberOfProducts.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="+****************"
                    {...register("phone")}
                    className="placeholder:text-muted"
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-400">{errors.phone.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website/Store Link</Label>
                  <Input
                    id="website"
                    type="url"
                    placeholder="https://your-store.com"
                    {...register("website")}
                    className="placeholder:text-muted"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="referral">How did you hear about us?</Label>
                  <Input
                    id="referral"
                    placeholder="e.g., Social media, Friend, Search engine"
                    {...register("referral")}
                    className="placeholder:text-muted"
                  />
                </div>

                <div className="flex gap-3">
                  <Button 
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => setValue("userType", "consumer")}
                    disabled={isLoading}
                  >
                    Back to Consumer
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 font-medium"
                    disabled={isLoading}
                  >
                    {isLoading 
                      ? `Creating Account & Submitting Application... (${signupStep})`
                      : "Create Account & Submit Application"
                    }
                  </Button>
                </div>
              </form>
            </div>
          ) : (
            // Show regular auth form for consumers or login
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Debug info */}
              {process.env.NODE_ENV === 'development' && (
                <div className="p-2 bg-yellow-100 text-xs text-black rounded">
                  Debug: Mode={mode}, UserType={watch("userType")}, Errors={Object.keys(errors).length}
                </div>
              )}
              {mode === "signup" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="name">First Name</Label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="John"
                      {...register("name")}
                      className={cn(
                        "placeholder:text-muted",
                        errors.name && "border-red-500"
                      )}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-400">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      type="text"
                      placeholder="Doe"
                      {...register("lastName")}
                      className={cn(
                        "placeholder:text-muted",
                        errors.lastName && "border-red-500"
                      )}
                    />
                    {errors.lastName && (
                      <p className="text-sm text-red-400">{errors.lastName.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Account Type</Label>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant={userType === "consumer" ? "default" : "outline"}
                        className="flex-1"
                        onClick={() => setValue("userType", "consumer")}
                      >
                        Consumer
                      </Button>
                      <Button
                        type="button"
                        variant={userType === "seller" ? "default" : "outline"}
                        className="flex-1"
                        onClick={() => setValue("userType", "seller")}
                      >
                        Seller
                      </Button>
                    </div>
                  </div>
                </>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email")}
                  className={cn(
                    "placeholder:text-muted",
                    errors.email && "border-red-500"
                  )}
                />
                {errors.email && (
                  <p className="text-sm text-red-400">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="********"
                    {...register("password")}
                    className={cn(
                      "placeholder:text-muted pr-10",
                      errors.password && "border-red-500"
                    )}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-400">{errors.password.message}</p>
                )}
              </div>

              {mode === "signup" && (
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="********"
                      {...register("confirmPassword")}
                      className={cn(
                        "placeholder:text-muted pr-10",
                        errors.confirmPassword && "border-red-500"
                      )}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-400">{errors.confirmPassword.message}</p>
                  )}
                </div>
              )}

              <Button 
                type="submit" 
                className="w-full font-medium"
                disabled={isLoading}
                onClick={() => {
                  console.log("Form validation state:", {
                    isValid: Object.keys(errors).length === 0,
                    errors,
                    formValues: watch(),
                    mode
                  });
                }}
              >
                {isLoading 
                  ? (mode === "login" ? "Signing in..." : "Creating account...") 
                  : (mode === "login" ? "Sign In" : "Create Account")
                }
              </Button>
            </form>
          )}

          <div className="text-center text-sm text-zinc-400">
            {mode === "login" ? (
              <>
                Don't have an account?{" "}
                <button
                  type="button"
                  className="cursor-pointer hover:underline text-primary"
                  onClick={() => setMode("signup")}
                >
                  Sign up
                </button>
              </>
            ) : (
              <>
                Already have an account?{" "}
                <button
                  type="button"
                  className="cursor-pointer hover:underline text-primary"
                  onClick={() => setMode("login")}
                >
                  Sign in
                </button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}