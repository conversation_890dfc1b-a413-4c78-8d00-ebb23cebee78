"use client";

import { ReactNode } from "react";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";

interface CommunityLayoutProps {
  children: ReactNode;
  showHeader?: boolean;
}

export function CommunityLayout({ children, showHeader = true }: CommunityLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {showHeader && <MarketplaceHeader />}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
