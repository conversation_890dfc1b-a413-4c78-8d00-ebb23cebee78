"use client";

import { useState, useEffect } from "react";
import { Badge } from "@repo/ui/components/badge";
import { But<PERSON> } from "@repo/ui/components/button";
import { Heart, Eye, Clock, Users, Gavel } from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import Link from "next/link";

interface AuctionCardProps {
  auction: {
    _id: string;
    title: string;
    images: string[];
    currentBid: number;
    startingBid: number;
    startTime: number;
    endTime: number;
    status: string;
    category: string;
    brand: string;
    condition: string;
    totalBids: number;
    uniqueBidders: number;
    views: number;
    watchers: number;
    reserveMet: boolean;
    timeRemaining: number;
    isActive: boolean;
    winningBid?: number;
  };
}

export function AuctionCard({ auction }: AuctionCardProps) {
  const [timeLeft, setTimeLeft] = useState(auction.timeRemaining);
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    // Update timer for all non-ended auctions (scheduled and active)
    if (auction.status === "ended" || auction.status === "sold") return;

    const timer = setInterval(() => {
      const now = Date.now();
      let remaining: number;
      
      if (auction.status === "scheduled") {
        // For scheduled auctions, show time until start
        remaining = Math.max(0, auction.startTime - now);
      } else if (auction.status === "active") {
        // For active auctions, show time until end
        remaining = Math.max(0, auction.endTime - now);
      } else {
        remaining = 0;
      }
      
      setTimeLeft(remaining);
    }, 1000);

    return () => clearInterval(timer);
  }, [auction.startTime, auction.endTime, auction.status]);

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return "Ended";
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getTimeColor = () => {
    if (auction.status === "ended" || auction.status === "sold") return "text-muted-foreground";
    if (auction.status === "scheduled") return "text-blue-600"; // Scheduled auctions
    if (auction.status === "active") {
      if (timeLeft < 60 * 60 * 1000) return "text-destructive"; // Less than 1 hour
      if (timeLeft < 24 * 60 * 60 * 1000) return "text-orange-600"; // Less than 1 day
      return "text-green-600";
    }
    return "text-muted-foreground";
  };

  const getTimeLabel = () => {
    if (auction.status === "scheduled") return "Starts in";
    if (auction.status === "active") return "Time left";
    return "Time remaining";
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === auction.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? auction.images.length - 1 : prev - 1
    );
  };

  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // TODO: Implement auction watching functionality
  };

  return (
    <Link href={`/auctions/${auction._id}`}>
      <div
        className={cn(
          "group relative bg-card rounded-3xl border border-border/50 overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105",
          "cursor-pointer hover:border-border"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image Container */}
        <div className="relative aspect-square overflow-hidden bg-muted">
          {auction.images.length > 0 && auction.images[currentImageIndex] && auction.images[currentImageIndex].trim() ? (
            <>
              <Image
                src={auction.images[currentImageIndex]}
                alt={auction.title}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                onError={(e) => {
                  const target = e.target as HTMLElement;
                  const container = target.closest('.relative');
                  if (container) {
                    container.innerHTML = `
                      <div class="w-full h-full flex items-center justify-center text-muted-foreground">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM9 6v10h6V6H9z"></path>
                        </svg>
                      </div>
                    `;
                  }
                }}
              />
              
              {/* Image Navigation */}
              {auction.images.length > 1 && isHovered && (
                <>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      prevImage();
                    }}
                    className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-background/80 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      nextImage();
                    }}
                    className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-background/80 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}

              {/* Image Indicators */}
              {auction.images.length > 1 && (
                <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                  {auction.images.map((_, index) => (
                    <div
                      key={index}
                      className={cn(
                        "w-1.5 h-1.5 rounded-full transition-colors duration-200",
                        index === currentImageIndex
                          ? "bg-card"
                          : "bg-card/50"
                      )}
                    />
                  ))}
                </div>
              )}
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <Gavel className="w-12 h-12" />
            </div>
          )}

          {/* Status Badge */}
          <Badge
            className={cn(
              "absolute top-3 left-3 text-xs font-medium",
              auction.isActive
                ? "bg-green-100/80 text-green-800 border-green-200"
                : auction.status === "scheduled"
                ? "bg-blue-100/80 text-blue-800 border-blue-200"
                : auction.status === "sold"
                ? "bg-purple-100/80 text-purple-800 border-purple-200"
                : "bg-gray-100/80 text-gray-800 border-gray-200"
            )}
          >
            {auction.isActive ? "Live" : auction.status === "scheduled" ? "Scheduled" : auction.status === "sold" ? "Sold" : "Ended"}
          </Badge>

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Button
              size="sm"
              variant="secondary"
              className="w-8 h-8 p-0 bg-background/80 hover:bg-background rounded-xl"
              onClick={handleFavorite}
            >
              <Heart className="w-4 h-4 transition-colors duration-200" />
            </Button>
          </div>
        </div>

        {/* Auction Info */}
        <div className="p-4 space-y-3">
          <div className="space-y-1">
            <p className="text-xs font-light text-muted uppercase tracking-wide">
              {auction.brand}
            </p>
            <h3 className="font-light text-card-foreground line-clamp-2 leading-tight">
              {auction.title}
            </h3>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                {auction.status === "active" && timeLeft < 60 * 60 * 1000 && (
                  <Badge className="bg-destructive/10 text-destructive text-xs px-1 py-0">
                    Ending Soon
                  </Badge>
                )}
                {auction.status === "scheduled" && (
                  <Badge className="bg-blue-100/80 text-blue-800 text-xs px-1 py-0">
                    Scheduled
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-2">
              <Clock className={`w-3 h-3 ${getTimeColor()}`} />
                <div className="flex flex-row items-center gap-1">
                    {auction.status === "ended" && (
                    <span className={`text-xs font-light ${getTimeColor()}`}>
                        {getTimeLabel()}
                    </span>
                    )}
                  <span className={`text-xs font-medium ${getTimeColor()}`}>
                    {formatTimeRemaining(timeLeft)}
                  </span>
                </div>
                </div>
              <p className={`text-lg font-light ${
                auction.status === "sold" 
                  ? "text-green-600" 
                  : auction.status === "ended"
                    ? "text-orange-600"
                    : "text-card-foreground"
              }`}>
                {auction.status === "scheduled" ? (
                  <>Starting bid: {formatCurrency(auction.startingBid)}</>
                ) : auction.status === "sold" ? (
                  <>Sold for: {formatCurrency(auction.winningBid || auction.currentBid)}</>
                ) : auction.status === "ended" ? (
                  <>Final bid: {formatCurrency(auction.currentBid)}</>
                ) : (
                  <>Current bid: {formatCurrency(auction.currentBid)}</>
                )}
                {auction.status === "sold" && (
                  <Badge className="ml-2 bg-green-100 text-green-800 text-xs">
                    SOLD
                  </Badge>
                )}
                {auction.status === "ended" && (
                  <Badge className="ml-2 bg-orange-100 text-orange-800 text-xs">
                    ENDED
                  </Badge>
                )}
              </p>
              {auction.currentBid > auction.startingBid && auction.status !== "scheduled" && (
                <p className="text-xs text-muted">
                  Started at {formatCurrency(auction.startingBid)}
                </p>
              )}
            </div>
            <Badge variant="secondary" className="text-xs font-light rounded-full bg-muted/50">
              {auction.category}
            </Badge>
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between text-xs text-muted pt-2 border-t border-border/50">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Gavel className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">{auction.totalBids}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-3 h-3 text-muted-foreground" />
                <span className="text-muted-foreground">{auction.uniqueBidders}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3 text-muted-foreground " />
              <span className="text-muted-foreground">{auction.views || 0}</span>
            </div>
          </div>
        </div>

        {/* Hover Overlay */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        )} />
      </div>
    </Link>
  );
}
