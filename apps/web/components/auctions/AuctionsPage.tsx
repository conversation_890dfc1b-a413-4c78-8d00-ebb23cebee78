"use client";

import { useState, useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Gavel, 
  Search, 
  Filter, 
  Clock, 
  Users, 
  DollarSign, 
  TrendingUp,
  ArrowLeft,
  SlidersHorizontal
} from "lucide-react";
import { AuctionCard } from "./AuctionCard";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";

export interface AuctionFilterState {
  categories: string[];
  status: string[];
  searchQuery: string;
}

const initialFilters: AuctionFilterState = {
  categories: [],
  status: [],
  searchQuery: "",
};

const CATEGORIES = [
  { value: "clothing", label: "Clothing" },
  { value: "sneakers", label: "Sneakers" },
  { value: "collectibles", label: "Collectibles" },
  { value: "accessories", label: "Accessories" },
  { value: "handbags", label: "Handbags" },
  { value: "jewelry", label: "Jewelry" },
  { value: "watches", label: "Watches" },
  { value: "sunglasses", label: "Sunglasses" },
  { value: "cars", label: "Cars" },
  { value: "art", label: "Art" },
];

const STATUS_OPTIONS = [
  { value: "active", label: "Active" },
  { value: "scheduled", label: "Scheduled" },
  { value: "ended", label: "Ended" },
  { value: "sold", label: "Sold" },
];

export function AuctionsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [filters, setFilters] = useState<AuctionFilterState>(initialFilters);
  const [sortBy, setSortBy] = useState<"newest" | "ending_soon" | "price_high" | "price_low">("newest");

  // Check subscription status
  const isSubscribed = user?.subscriptionStatus === "active";

  // Fetch auctions
  const auctionsData = useQuery(api.auctions.getAuctions, {
    status: filters.status.length === 1 ? filters.status[0] as any : undefined,
    category: filters.categories.length === 1 ? filters.categories[0] : undefined,
    limit: 50,
  });

  const auctions = auctionsData?.auctions || [];
  
  // Apply client-side filtering and sorting
  const filteredAuctions = useMemo(() => {
    let filtered = auctions;

    // Search filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(auction =>
        auction.title.toLowerCase().includes(query) ||
        auction.brand.toLowerCase().includes(query) ||
        auction.description.toLowerCase().includes(query)
      );
    }

    // Category filter (if multiple selected)
    if (filters.categories.length > 1) {
      filtered = filtered.filter(auction => 
        filters.categories.includes(auction.category)
      );
    }

    // Status filter (if multiple selected)
    if (filters.status.length > 1) {
      filtered = filtered.filter(auction => 
        filters.status.includes(auction.status)
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "ending_soon":
          return a.timeRemaining - b.timeRemaining;
        case "price_high":
          return b.currentBid - a.currentBid;
        case "price_low":
          return a.currentBid - b.currentBid;
        case "newest":
        default:
          return b.startTime - a.startTime;
      }
    });

    return filtered;
  }, [auctions, filters, sortBy]);

  // Calculate stats
  const activeAuctions = auctions.filter(a => a.isActive).length;
  const totalBids = auctions.reduce((sum, a) => sum + a.totalBids, 0);
  const totalValue = auctions.reduce((sum, a) => sum + a.currentBid, 0);
  const endingSoon = auctions.filter(a => a.isActive && a.timeRemaining < 24 * 60 * 60 * 1000).length;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      notation: amount > 1000000 ? 'compact' : 'standard',
    }).format(amount);
  };

  const handleFilterChange = (newFilters: Partial<AuctionFilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
  };

  const clearFilters = () => {
    setFilters(initialFilters);
  };

  const activeFilterCount = useMemo(() => {
    return (
      filters.categories.length +
      filters.status.length +
      (filters.searchQuery ? 1 : 0)
    );
  }, [filters]);

  // Subscription gate for non-subscribers
  // if (!isSubscribed) {
  //   return (
  //     <div className="min-h-screen bg-gradient-to-br from-primary-light to-white">
  //       <div className="max-w-4xl mx-auto px-6 py-12">
  //         <Button
  //           variant="ghost"
  //           onClick={() => router.back()}
  //           className="mb-6 text-secondary-sage hover:text-primary-dark"
  //         >
  //           <ArrowLeft className="w-4 h-4 mr-2" />
  //           Back
  //         </Button>

  //         <Card className="p-12 text-center bg-white shadow-lg">
  //           <div className="w-20 h-20 bg-gradient-to-br from-accent-mauve to-tertiary-tan rounded-full flex items-center justify-center mx-auto mb-6">
  //             <Gavel className="w-10 h-10 text-white" />
  //           </div>
            
  //           <h1 className="text-4xl font-light text-primary-dark mb-4">
  //             Exclusive Auctions
  //           </h1>
            
  //           <p className="text-secondary-sage text-lg mb-8 max-w-2xl mx-auto">
  //             Access live luxury auctions featuring rare timepieces, designer handbags, 
  //             collectible sneakers, and authenticated luxury goods from verified sellers.
  //           </p>
            
  //           <div className="bg-accent-mauve/10 border border-accent-mauve/20 rounded-lg p-6 mb-8 max-w-md mx-auto">
  //             <h3 className="font-semibold text-accent-mauve mb-3">Premium Features Include:</h3>
  //             <ul className="text-accent-mauve text-sm space-y-2">
  //               <li>• Real-time bidding on luxury items</li>
  //               <li>• Authenticated goods from verified sellers</li>
  //               <li>• Exclusive access to rare collections</li>
  //               <li>• Professional item photography & descriptions</li>
  //               <li>• Secure payment & shipping</li>
  //             </ul>
  //           </div>

  //           <div className="flex flex-col sm:flex-row gap-4 justify-center">
  //             <Button
  //               onClick={() => router.push("/subscribe")}
  //               className="bg-primary-dark hover:bg-primary-dark/90 text-primary-light px-8 py-3 text-lg"
  //             >
  //               Subscribe for $20/month
  //             </Button>
  //             <Button
  //               variant="outline"
  //               onClick={() => router.push("/marketplace")}
  //               className="border-secondary-sage text-secondary-sage hover:bg-secondary-sage/10"
  //             >
  //               Browse Marketplace
  //             </Button>
  //           </div>
  //         </Card>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="bg-card min-h-screen">
      <MarketplaceHeader onSearch={handleSearch} />
      
      <div className="">
        {/* Page Title and Controls */}
        <div className="bg-white border-b">
          <div className="container mx-auto px-6 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Live Auctions</h1>
                <p className="text-muted-foreground">
                  Bid on authenticated luxury items from verified sellers
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                <Select 
                  value={sortBy} 
                  onValueChange={(value: any) => setSortBy(value)}
                >
                  <SelectTrigger className="w-48">
                    <SlidersHorizontal className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="ending_soon">Ending Soon</SelectItem>
                    <SelectItem value="price_high">Highest Bid</SelectItem>
                    <SelectItem value="price_low">Lowest Bid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Browse Hero Section */}
        <div className="bg-gradient-to-r from-primary/5 to-secondary/5 border-b">
          <div className="container mx-auto px-6 py-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Live Luxury Auctions</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Discover exclusive auction opportunities featuring authenticated luxury goods from verified sellers. 
                Place bids on rare timepieces, designer handbags, collectible sneakers, and more.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Auctions</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{activeAuctions}</div>
                  <p className="text-xs text-muted-foreground">Live bidding now</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Bids</CardTitle>
                  <Gavel className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalBids}</div>
                  <p className="text-xs text-muted-foreground">Across all auctions</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(totalValue)}</div>
                  <p className="text-xs text-muted-foreground">Current bid total</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Ending Soon</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{endingSoon}</div>
                  <p className="text-xs text-muted-foreground">Less than 24h left</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
        
        {/* Main Content Layout */}
        <div className="flex gap-6 container mx-auto px-6 py-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Filters</h3>
              
              {/* Category Filter */}
              <div className="space-y-3 mb-6">
                <h4 className="text-sm font-medium">Categories</h4>
                <div className="space-y-2">
                  {CATEGORIES.map((category) => (
                    <label key={category.value} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={filters.categories.includes(category.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleFilterChange({
                              categories: [...filters.categories, category.value]
                            });
                          } else {
                            handleFilterChange({
                              categories: filters.categories.filter(c => c !== category.value)
                            });
                          }
                        }}
                        className="rounded border-border"
                      />
                      <span>{category.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Status</h4>
                <div className="space-y-2">
                  {STATUS_OPTIONS.map((status) => (
                    <label key={status.value} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={filters.status.includes(status.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleFilterChange({
                              status: [...filters.status, status.value]
                            });
                          } else {
                            handleFilterChange({
                              status: filters.status.filter(s => s !== status.value)
                            });
                          }
                        }}
                        className="rounded border-border"
                      />
                      <span>{status.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Clear Filters */}
              {activeFilterCount > 0 && (
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="w-full mt-4"
                >
                  Clear All Filters
                </Button>
              )}
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <Card className="p-4">
                <h3 className="font-semibold mb-2">Quick Filters</h3>
                <div className="flex flex-wrap gap-2">
                  {CATEGORIES.slice(0, 4).map((category) => (
                    <Button
                      key={category.value}
                      variant={filters.categories.includes(category.value) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (filters.categories.includes(category.value)) {
                          handleFilterChange({
                            categories: filters.categories.filter(c => c !== category.value)
                          });
                        } else {
                          handleFilterChange({
                            categories: [...filters.categories, category.value]
                          });
                        }
                      }}
                    >
                      {category.label}
                    </Button>
                  ))}
                </div>
              </Card>
            </div>

            {/* Filter Pills */}
            {activeFilterCount > 0 && (
          <div className="flex items-center gap-2 mb-6">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {filters.categories.map((category) => (
              <Badge key={category} variant="secondary" className="gap-1">
                {CATEGORIES.find(c => c.value === category)?.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => handleFilterChange({
                    categories: filters.categories.filter(c => c !== category)
                  })}
                >
                  ×
                </Button>
              </Badge>
            ))}
            {filters.status.map((status) => (
              <Badge key={status} variant="secondary" className="gap-1">
                {STATUS_OPTIONS.find(s => s.value === status)?.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => handleFilterChange({
                    status: filters.status.filter(s => s !== status)
                  })}
                >
                  ×
                </Button>
              </Badge>
            ))}
            {filters.searchQuery && (
              <Badge variant="secondary" className="gap-1">
                Search: "{filters.searchQuery}"
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => handleSearch("")}
                >
                  ×
                </Button>
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-muted-foreground hover:text-foreground"
            >
              Clear all
            </Button>
          </div>
        )}

        {/* Auction Grid */}
        {filteredAuctions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAuctions.map((auction) => (
              <AuctionCard key={auction._id} auction={auction} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <Gavel className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
            <h3 className="text-xl font-semibold mb-2">No auctions found</h3>
            <p className="text-muted-foreground mb-4">
              {activeFilterCount > 0
                ? "Try adjusting your filters or search terms"
                : "Check back soon for new luxury auctions"
              }
            </p>
            {activeFilterCount > 0 && (
              <Button
                variant="outline"
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            )}
            </div>
          )}
          </div>
        </div>
      </div>
    </div>
  );
}
