"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Label } from "@repo/ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Switch } from "@repo/ui/components/switch";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  ArrowLeft,
  Users,
  Hash,
  Globe,
  Lock,
  Info,
  Palette,
  Shield,
} from "lucide-react";
import Link from "next/link";

const COMMUNITY_ICONS = [
  "💬", "🎯", "💡", "🚀", "⭐", "🔥", "💎", "🏆", "🎨", "🔧",
  "📚", "🎵", "🎮", "📱", "💻", "🌟", "🎪", "🎭", "🎲", "🎸"
];

const COMMUNITY_COLORS = [
  "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", 
  "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
];

export default function CreateCommunityForm() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [description, setDescription] = useState("");
  const [icon, setIcon] = useState("💬");
  const [color, setColor] = useState("#4ECDC4");
  const [isPrivate, setIsPrivate] = useState(false);
  const [rules, setRules] = useState("");

  const createCommunity = useMutation(api.forum.createForumGroup);

  // Auto-generate slug from name
  const handleNameChange = (value: string) => {
    setName(value);
    // Generate slug: lowercase, replace spaces with hyphens, remove special chars
    const generatedSlug = value
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    setSlug(generatedSlug);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentUser) {
      toast.error("Please sign in to create a community");
      return;
    }

    if (!name.trim() || !slug.trim() || !description.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (slug.length < 3) {
      toast.error("Community name must be at least 3 characters");
      return;
    }

    if (description.length < 10) {
      toast.error("Description must be at least 10 characters");
      return;
    }

    setIsSubmitting(true);

    try {
      const community = await createCommunity({
        name: name.trim(),
        slug: slug.trim(),
        description: description.trim(),
        icon,
        color,
        isPrivate,
        rules: rules.trim() || undefined,
      });

      toast.success("Community created successfully!");
      router.push(`/forum/groups/${slug}`);
    } catch (error: any) {
      toast.error(error.message || "Failed to create community");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!currentUser) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="pt-8 pb-8">
            <div className="text-center">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Sign in Required</h2>
              <p className="text-muted-foreground mb-4">
                You need to be signed in to create a community.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => router.push("/forum")} className="gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back to Forum
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Create a Community</h1>
          <p className="text-muted-foreground">
            Build a community around your interests
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Community Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Luxury Watches"
                value={name}
                onChange={(e) => handleNameChange(e.target.value)}
                maxLength={50}
                required
              />
              <p className="text-xs text-muted-foreground">
                Choose a clear, descriptive name for your community
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Community URL *</Label>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">r/</span>
                <Input
                  id="slug"
                  placeholder="luxury-watches"
                  value={slug}
                  onChange={(e) => setSlug(e.target.value)}
                  pattern="[a-z0-9-]+"
                  maxLength={30}
                  required
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Only lowercase letters, numbers, and hyphens. Cannot be changed later.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                placeholder="Describe what your community is about..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                maxLength={500}
                required
              />
              <p className="text-xs text-muted-foreground">
                {description.length}/500 characters
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              Appearance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Community Icon</Label>
              <div className="flex flex-wrap gap-2">
                {COMMUNITY_ICONS.map((iconOption) => (
                  <Button
                    key={iconOption}
                    type="button"
                    variant={icon === iconOption ? "default" : "outline"}
                    size="sm"
                    onClick={() => setIcon(iconOption)}
                    className="w-10 h-10 p-0 text-lg"
                  >
                    {iconOption}
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Community Color</Label>
              <div className="flex flex-wrap gap-2">
                {COMMUNITY_COLORS.map((colorOption) => (
                  <Button
                    key={colorOption}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setColor(colorOption)}
                    className="w-10 h-10 p-0 border-2"
                    style={{ 
                      backgroundColor: colorOption,
                      borderColor: color === colorOption ? '#000' : colorOption
                    }}
                  >
                    {color === colorOption && (
                      <span className="text-white font-bold">✓</span>
                    )}
                  </Button>
                ))}
              </div>
            </div>

            {/* Preview */}
            <div className="space-y-2">
              <Label>Preview</Label>
              <div className="flex items-center gap-2 p-3 border rounded-md">
                <span className="text-lg">{icon}</span>
                <div>
                  <div className="font-medium">r/{slug || "community-name"}</div>
                  <div className="text-sm text-muted-foreground">
                    {name || "Community Name"}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy & Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Privacy & Rules
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="flex items-center gap-2">
                  {isPrivate ? <Lock className="w-4 h-4" /> : <Globe className="w-4 h-4" />}
                  {isPrivate ? "Private Community" : "Public Community"}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {isPrivate 
                    ? "Only approved members can view and post"
                    : "Anyone can view and participate"
                  }
                </p>
              </div>
              <Switch
                checked={isPrivate}
                onCheckedChange={setIsPrivate}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="rules">Community Rules (Optional)</Label>
              <Textarea
                id="rules"
                placeholder="1. Be respectful to all members&#10;2. Stay on topic&#10;3. No spam or self-promotion"
                value={rules}
                onChange={(e) => setRules(e.target.value)}
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-muted-foreground">
                Set guidelines for your community members
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex gap-3">
          <Button
            type="submit"
            disabled={isSubmitting || !name.trim() || !slug.trim() || !description.trim()}
            className="flex-1"
          >
            {isSubmitting ? "Creating..." : "Create Community"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/forum")}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}
