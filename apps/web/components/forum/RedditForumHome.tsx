"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import RedditStylePost from "./RedditStylePost";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  Plus,
  Search,
  TrendingUp,
  Clock,
  Award as AwardIcon,
  MessageSquare,
  Eye,
  Filter,
  ChevronDown,
  Users,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Card } from "@repo/ui/components/card";
import { ProfileImage } from "@/components/common/ProfileImage";
import { cn } from "@repo/ui/lib/utils";

type SortType = "hot" | "new" | "top" | "controversial";
type TimeRange = "hour" | "day" | "week" | "month" | "year" | "all";
type ViewMode = "card" | "compact";

export default function RedditForumHome() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [sortBy, setSortBy] = useState<SortType>("hot");
  const [timeRange, setTimeRange] = useState<TimeRange>("all");
  const [selectedGroup, setSelectedGroup] = useState<string | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState("");
  const [cursor, setCursor] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("card");
  const [selectedPost, setSelectedPost] = useState<any | null>(null);

  // Fetch groups for sidebar
  const groups = useQuery(api.forum.getForumGroups, {
    includePrivate: false,
  });

  // Fetch posts
  const posts = useQuery(api.forum.getForumPosts, {
    groupId: selectedGroup as any,
    sortBy: sortBy === "hot" ? "popular" : sortBy === "new" ? "recent" : "popular",
    paginationOpts: { numItems: 25, cursor },
  });

  // Fetch awards
  const awards = useQuery(api.redditForum.getAwards, {});

  // Fetch real community data
  const communityStats = useQuery(api.members.getCommunityStats, {});
  const topContributors = useQuery(api.members.getTopContributors, { limit: 5 });

  // Seed awards on first load
  const seedAwards = useMutation(api.seedAwards.seedDefaultAwards);
  
  useEffect(() => {
    if (awards && awards.length === 0) {
      seedAwards().catch(console.error);
    }
  }, [awards]);

  const getSortIcon = (sort: SortType) => {
    switch (sort) {
      case "hot":
        return <TrendingUp className="h-4 w-4" />;
      case "new":
        return <Clock className="h-4 w-4" />;
      case "top":
        return <AwardIcon className="h-4 w-4" />;
      case "controversial":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <div className="flex-1">
        {/* Sort Tabs */}
        <Card className="mb-4 p-2">
          <div className="flex items-center justify-between">
            <Tabs value={sortBy} onValueChange={(v) => setSortBy(v as SortType)}>
              <TabsList className="h-9">
                <TabsTrigger value="hot" className="gap-1">
                  <TrendingUp className="h-3 w-3" />
                  Hot
                </TabsTrigger>
                <TabsTrigger value="new" className="gap-1">
                  <Clock className="h-3 w-3" />
                  New
                </TabsTrigger>
                <TabsTrigger value="top" className="gap-1">
                  <AwardIcon className="h-3 w-3" />
                  Top
                </TabsTrigger>
                <TabsTrigger value="controversial" className="gap-1">
                  <MessageSquare className="h-3 w-3" />
                  Controversial
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {sortBy === "top" && (
              <Select value={timeRange} onValueChange={(v) => setTimeRange(v as TimeRange)}>
                <SelectTrigger className="w-32 h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hour">Past Hour</SelectItem>
                  <SelectItem value="day">Past 24 Hours</SelectItem>
                  <SelectItem value="week">Past Week</SelectItem>
                  <SelectItem value="month">Past Month</SelectItem>
                  <SelectItem value="year">Past Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
            )}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <Filter className="h-3 w-3" />
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setViewMode("compact")}>
                  <Eye className="h-4 w-4 mr-2" />
                  Compact View
                  {viewMode === "compact" && <span className="ml-auto text-xs">✓</span>}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setViewMode("card")}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Card View
                  {viewMode === "card" && <span className="ml-auto text-xs">✓</span>}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </Card>

        {/* Posts List */}
        <div className={cn("space-y-2", viewMode === "compact" && "space-y-1")}>
          {posts?.page && posts.page.length > 0 ? (
            posts.page
              .filter(post => !searchTerm || 
                post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                post.contentPlainText?.toLowerCase().includes(searchTerm.toLowerCase())
              )
              .map((post: any) => (
                <RedditStylePost
                  key={post._id}
                  post={post}
                  showContent={viewMode === "card"}
                  viewMode={viewMode}
                  onAwardClick={() => {/* Open award modal */}}
                  onPostClick={(post) => router.push(`/forum/post/${post._id}`)}
                />
              ))
          ) : (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">
                No posts yet. Be the first to share something!
              </p>
              {currentUser && (
                <Button 
                  className="mt-4"
                  onClick={() => router.push("/forum/submit")}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create Post
                </Button>
              )}
            </Card>
          )}
        </div>

        {/* Load More */}
        {posts && !posts.isDone && (
          <div className="flex justify-center mt-6">
            <Button
              variant="outline"
              onClick={() => setCursor(posts.continueCursor)}
            >
              Load More
            </Button>
          </div>
        )}
      </div>

      {/* Sidebar */}
      <div className="w-80 space-y-4">
        {/* Create Post Button */}
        {currentUser && (
          <div className="space-y-2">
            <Button 
              className="w-full" 
              onClick={() => router.push("/forum/submit")}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Post
            </Button>
            <Button 
              variant="outline"
              className="w-full" 
              onClick={() => router.push("/forum/create-community")}
            >
              <Users className="mr-2 h-4 w-4" />
              Create Community
            </Button>
          </div>
        )}

        {/* About Section */}
        <Card className="p-4">
          <h3 className="font-semibold mb-2">About Community</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Welcome to HauteVault's community forum. Share knowledge, ask questions, 
            and connect with fellow luxury enthusiasts.
          </p>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Members</span>
              <span className="font-medium">
                {communityStats?.totalMembers?.toLocaleString() || "0"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Online</span>
              <span className="font-medium">
                {communityStats?.onlineMembers?.toLocaleString() || "0"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Posts</span>
              <span className="font-medium">
                {communityStats?.totalPosts?.toLocaleString() || "0"}
              </span>
            </div>
          </div>
        </Card>

        {/* Popular Groups */}
        {groups && groups.length > 0 && (
          <Card className="p-4">
            <h3 className="font-semibold mb-3">Communities</h3>
            <div className="space-y-2">
              <Button
                variant={!selectedGroup ? "secondary" : "ghost"}
                className="w-full justify-start text-sm"
                onClick={() => setSelectedGroup(undefined)}
              >
                All Communities
              </Button>
              {groups.slice(0, 5).map((group: any) => (
                <div key={group._id} className="flex gap-1">
                  <Button
                    variant={selectedGroup === group._id ? "secondary" : "ghost"}
                    className="flex-1 justify-start text-sm"
                    onClick={() => setSelectedGroup(group._id)}
                  >
                    <span className="mr-2">{group.icon}</span>
                    r/{group.slug}
                    <span className="ml-auto text-xs text-muted-foreground">
                      {group.memberCount}
                    </span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="px-2"
                    onClick={() => router.push(`/forum/groups/${group.slug}`)}
                    title={`Visit r/${group.slug}`}
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                </div>
              ))}
              <Link href="/forum/groups">
                <Button variant="link" className="w-full text-sm">
                  View All Communities →
                </Button>
              </Link>
            </div>
          </Card>
        )}

        {/* Top Contributors */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Top Contributors</h3>
          <div className="space-y-3">
            {topContributors && topContributors.length > 0 ? (
              topContributors.map((contributor: any, index: number) => (
                <Link 
                  key={contributor._id}
                  href={`/members/${contributor.userId}`}
                  className="flex items-center gap-2 hover:bg-muted/50 rounded-md p-1 -m-1 transition-colors"
                >
                  <span className="text-sm font-medium text-muted-foreground w-4">
                    {index + 1}.
                  </span>
                  <ProfileImage
                    storageId={contributor.profileImage}
                    name={contributor.displayName}
                    size="sm"
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium flex items-center gap-1">
                      {contributor.displayName}
                      {contributor.isTopContributor && (
                        <AwardIcon className="h-3 w-3 text-yellow-500" />
                      )}
                      {contributor.isPremium && (
                        <span className="text-xs bg-yellow-500 text-white px-1 rounded">
                          ⭐
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {contributor.totalKarma > 0 
                        ? `${contributor.totalKarma.toLocaleString()} karma`
                        : `${contributor.totalForumPosts} posts, ${contributor.totalComments} comments`
                      }
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="text-sm text-muted-foreground text-center py-2">
                No contributors yet. Be the first to post!
              </div>
            )}
          </div>
        </Card>

        {/* Reddit Rules */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Community Rules</h3>
          <ol className="space-y-2 text-sm">
            <li>1. Be respectful and civil</li>
            <li>2. No spam or self-promotion</li>
            <li>3. Keep content relevant</li>
            <li>4. No personal information</li>
            <li>5. Follow authentication guidelines</li>
          </ol>
        </Card>
      </div>
    </div>
  );
}
