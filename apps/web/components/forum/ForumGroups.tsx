"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  Users,
  Shield,
  Plus,
  Search,
  TrendingUp,
  MessageSquare,
  Calendar,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import { format } from "date-fns";

export default function ForumGroups() {
  const { user: currentUser } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateGroupOpen, setIsCreateGroupOpen] = useState(false);
  
  // New group form state
  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupSlug, setNewGroupSlug] = useState("");
  const [newGroupDescription, setNewGroupDescription] = useState("");
  const [newGroupIcon, setNewGroupIcon] = useState("");
  const [newGroupColor, setNewGroupColor] = useState("#000000");
  const [newGroupIsPrivate, setNewGroupIsPrivate] = useState(false);
  const [newGroupRules, setNewGroupRules] = useState("");

  // Fetch forum groups
  const groups = useQuery(api.forum.getForumGroups, {
    includePrivate: !!currentUser,
  });

  const createGroup = useMutation(api.forum.createForumGroup);
  const joinGroup = useMutation(api.forum.joinForumGroup);
  const leaveGroup = useMutation(api.forum.leaveForumGroup);

  const handleCreateGroup = async () => {
    if (!currentUser) {
      toast.error("Please sign in to create a group");
      return;
    }

    if (!newGroupName.trim() || !newGroupSlug.trim() || !newGroupDescription.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      await createGroup({
        name: newGroupName,
        slug: newGroupSlug.toLowerCase().replace(/\s+/g, "-"),
        description: newGroupDescription,
        icon: newGroupIcon || undefined,
        color: newGroupColor || undefined,
        isPrivate: newGroupIsPrivate,
        rules: newGroupRules || undefined,
      });
      
      toast.success("Group created successfully!");
      setIsCreateGroupOpen(false);
      resetForm();
    } catch (error: any) {
      toast.error(error.message || "Failed to create group");
    }
  };

  const handleJoinGroup = async (groupId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to join groups");
      return;
    }

    try {
      await joinGroup({ groupId: groupId as any });
      toast.success("Successfully joined the group!");
    } catch (error: any) {
      toast.error(error.message || "Failed to join group");
    }
  };

  const handleLeaveGroup = async (groupId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to leave groups");
      return;
    }

    try {
      await leaveGroup({ groupId: groupId as any });
      toast.success("Successfully left the group");
    } catch (error: any) {
      toast.error(error.message || "Failed to leave group");
    }
  };

  const resetForm = () => {
    setNewGroupName("");
    setNewGroupSlug("");
    setNewGroupDescription("");
    setNewGroupIcon("");
    setNewGroupColor("#000000");
    setNewGroupIsPrivate(false);
    setNewGroupRules("");
  };

  const filteredGroups = groups?.filter(group => 
    !searchTerm || 
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {currentUser && (
          <Dialog open={isCreateGroupOpen} onOpenChange={setIsCreateGroupOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Group
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Forum Group</DialogTitle>
                <DialogDescription>
                  Start a new community around a specific topic or interest
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Group Name *
                    </label>
                    <Input
                      placeholder="e.g., Sneaker Authentication"
                      value={newGroupName}
                      onChange={(e) => setNewGroupName(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      URL Slug *
                    </label>
                    <Input
                      placeholder="e.g., sneaker-auth"
                      value={newGroupSlug}
                      onChange={(e) => setNewGroupSlug(e.target.value)}
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Description *
                  </label>
                  <Textarea
                    placeholder="Describe what this group is about..."
                    value={newGroupDescription}
                    onChange={(e) => setNewGroupDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Icon (Emoji)
                    </label>
                    <Input
                      placeholder="e.g., 👟"
                      value={newGroupIcon}
                      onChange={(e) => setNewGroupIcon(e.target.value)}
                      maxLength={2}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Color
                    </label>
                    <Input
                      type="color"
                      value={newGroupColor}
                      onChange={(e) => setNewGroupColor(e.target.value)}
                    />
                  </div>
                  <div className="flex items-end">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={newGroupIsPrivate}
                        onChange={(e) => setNewGroupIsPrivate(e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm font-medium">Private Group</span>
                    </label>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Group Rules (optional)
                  </label>
                  <Textarea
                    placeholder="Any specific rules or guidelines for this group..."
                    value={newGroupRules}
                    onChange={(e) => setNewGroupRules(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => {
                    setIsCreateGroupOpen(false);
                    resetForm();
                  }}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateGroup}>
                    Create Group
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredGroups && filteredGroups.length > 0 ? (
          filteredGroups.map((group: any) => (
            <Card key={group._id} className="hover:shadow-lg transition-shadow">
              {group.bannerUrl && (
                <div 
                  className="h-32 bg-cover bg-center rounded-t-lg"
                  style={{ backgroundImage: `url(${group.bannerUrl})` }}
                />
              )}
              <CardHeader className={!group.bannerUrl ? "pb-4" : "pt-4 pb-4"}>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    {group.icon && (
                      <div 
                        className="text-3xl p-2 rounded-lg"
                        style={{ backgroundColor: group.color ? `${group.color}20` : "transparent" }}
                      >
                        {group.icon}
                      </div>
                    )}
                    <div>
                      <CardTitle className="text-lg">{group.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          {group.memberCount} members
                        </Badge>
                        {group.isPrivate && (
                          <Badge variant="outline" className="text-xs">
                            <Shield className="h-3 w-3 mr-1" />
                            Private
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {group.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <div className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    <span>{group.postCount} posts</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Created {format(new Date(group.updatedAt), "MMM yyyy")}</span>
                  </div>
                </div>

                {group.rules && (
                  <div className="p-3 bg-muted/30 rounded-lg mb-4">
                    <p className="text-xs font-medium mb-1">Group Rules</p>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {group.rules}
                    </p>
                  </div>
                )}

                <div className="flex gap-2">
                  {/* Use backend-provided membership status */}
                  {currentUser && (group.isCreator || group.isModerator) ? (
                    <div className="flex items-center gap-2 flex-1">
                      <Badge variant="secondary" className="gap-1">
                        <Shield className="w-3 h-3" />
                        {group.isCreator ? "Creator" : "Moderator"}
                      </Badge>
                      <Button 
                        variant="outline"
                        onClick={() => window.location.href = `/forum/groups/${group.slug}`}
                      >
                        View Community
                      </Button>
                    </div>
                  ) : currentUser && group.isMember ? (
                    <div className="flex items-center gap-2 flex-1">
                      <Badge variant="secondary" className="gap-1">
                        <Users className="w-3 h-3" />
                        Member
                      </Badge>
                      <Button 
                        variant="outline"
                        onClick={() => window.location.href = `/forum/groups/${group.slug}`}
                      >
                        View Community
                      </Button>
                    </div>
                  ) : (
                    <>
                      <Button 
                        variant="default" 
                        size="sm"
                        onClick={() => handleJoinGroup(group._id)}
                        disabled={!currentUser}
                      >
                        {!currentUser ? "Sign in to Join" : "Join Group"}
                      </Button>
                      <Button 
                        variant="outline"
                        className="flex-1"
                        onClick={() => window.location.href = `/forum/groups/${group.slug}`}
                      >
                        View Community
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {searchTerm 
                ? "No groups found matching your search" 
                : "No groups created yet"}
            </p>
            {currentUser && !searchTerm && (
              <Button className="mt-4" onClick={() => setIsCreateGroupOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create First Group
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
