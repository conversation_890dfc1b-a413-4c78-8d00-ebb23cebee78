"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  ShoppingBag, 
  MessageSquare, 
  Users, 
  UserCheck, 
  Scale, 
  Building, 
  Crown, 
  Gavel, 
  FileCheck, 
  ShieldCheck 
} from "lucide-react";

interface ThumbnailItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  category: "marketplace" | "forum" | "directory" | "services";
  badge?: string;
  comingSoon?: boolean;
}

const thumbnailData: ThumbnailItem[] = [
  {
    id: "marketplace-luxury",
    title: "Luxury Goods",
    description: "Premium luxury items and designer goods",
    icon: <Crown className="w-6 h-6" />,
    href: "/marketplace/luxury",
    category: "marketplace",
    badge: "Popular"
  },
  {
    id: "marketplace-watches", 
    title: "Watches",
    description: "Luxury timepieces and vintage watches",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/watches",
    category: "marketplace"
  },
  {
    id: "marketplace-jewelry",
    title: "Jewelry", 
    description: "Fine jewelry, diamonds, and precious stones",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/jewelry",
    category: "marketplace",
    badge: "New"
  },
  {
    id: "marketplace-cars",
    title: "Automotive",
    description: "Luxury cars, classics, and collectible vehicles",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/cars",
    category: "marketplace"
  },
  {
    id: "marketplace-art",
    title: "Art & Collectibles",
    description: "Fine art, sculptures, and rare collectibles",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/art",
    category: "marketplace"
  },
  {
    id: "marketplace-fashion",
    title: "Fashion",
    description: "Designer clothing and haute couture",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/fashion",
    category: "marketplace"
  },
  {
    id: "marketplace-sneakers",
    title: "Sneakers",
    description: "Limited edition and designer sneakers",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/sneakers",
    category: "marketplace"
  },
  {
    id: "marketplace-handbags",
    title: "Handbags",
    description: "Designer handbags and luxury accessories",
    icon: <ShoppingBag className="w-6 h-6" />,
    href: "/marketplace/handbags",
    category: "marketplace"
  },
  {
    id: "forum-1",
    title: "Forum 1",
    description: "Community discussions",
    icon: <MessageSquare className="w-6 h-6" />,
    href: "/forum",
    category: "forum"
  },
  {
    id: "forum-2",
    title: "Forum 2", 
    description: "Expert advice & tips",
    icon: <MessageSquare className="w-6 h-6" />,
    href: "/forum",
    category: "forum"
  },
  {
    id: "forum-3",
    title: "Forum 3",
    description: "Trading discussions",
    icon: <MessageSquare className="w-6 h-6" />,
    href: "/forum", 
    category: "forum"
  },
  {
    id: "seller-directory",
    title: "Seller Directory",
    description: "Browse verified sellers",
    icon: <UserCheck className="w-6 h-6" />,
    href: "/seller/directory",
    category: "directory"
  },
  {
    id: "member-directory", 
    title: "Member Directory",
    description: "Connect with members",
    icon: <Users className="w-6 h-6" />,
    href: "/members/directory",
    category: "directory"
  },
  {
    id: "dispute-center",
    title: "Dispute Center",
    description: "Resolution & support",
    icon: <Scale className="w-6 h-6" />,
    href: "/disputes",
    category: "services"
  },
  {
    id: "vendor-directory",
    title: "Vendor Directory", 
    description: "Business partnerships",
    icon: <Building className="w-6 h-6" />,
    href: "/vendors",
    category: "directory",
    comingSoon: true
  },
  {
    id: "staff-directory",
    title: "Staff Directory",
    description: "Platform team & support",
    icon: <Crown className="w-6 h-6" />,
    href: "/staff",
    category: "directory",
    comingSoon: true
  },
  {
    id: "auctions",
    title: "Auctions",
    description: "Live bidding events",
    icon: <Gavel className="w-6 h-6" />,
    href: "/auctions",
    category: "services",
    badge: "Live"
  },
  {
    id: "references",
    title: "References",
    description: "Seller & buyer reviews",
    icon: <FileCheck className="w-6 h-6" />,
    href: "/references",
    category: "services",
    comingSoon: true
  },
  {
    id: "warranty-check",
    title: "Warranty Check",
    description: "Product authenticity verification",
    icon: <ShieldCheck className="w-6 h-6" />,
    href: "/warranty",
    category: "services",
    comingSoon: true
  }
];

const categoryColors = {
  marketplace: "bg-blue-500/10 text-blue-600 border-blue-500/20",
  forum: "bg-green-500/10 text-green-600 border-green-500/20", 
  directory: "bg-purple-500/10 text-purple-600 border-purple-500/20",
  services: "bg-orange-500/10 text-orange-600 border-orange-500/20"
};

export function DashboardLanding() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const filteredThumbnails = selectedCategory === "all" 
    ? thumbnailData 
    : thumbnailData.filter(item => item.category === selectedCategory);

  const handleThumbnailClick = (item: ThumbnailItem) => {
    if (item.comingSoon) {
      return; // Do nothing for coming soon items
    }
    router.push(item.href);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100">
      {/* Header */}
      <div className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-light text-zinc-900 mb-2">
                Welcome to HauteVault
              </h1>
              <p className="text-zinc-600">
                Your gateway to luxury commerce and community
              </p>
            </div>
            
            {/* Category Filters */}
            <div className="flex gap-2">
              {["all", "marketplace", "forum", "directory", "services"].map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    selectedCategory === category
                      ? "bg-zinc-900 text-white"
                      : "bg-white text-zinc-600 hover:bg-zinc-100 border border-zinc-200"
                  }`}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="overflow-x-auto">
          <div className="flex gap-6 pb-4" style={{ width: "max-content" }}>
            {filteredThumbnails.map((item) => (
              <Card
                key={item.id}
                className={`relative group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 w-80 flex-shrink-0 ${
                  item.comingSoon 
                    ? "opacity-60 cursor-not-allowed" 
                    : "hover:shadow-2xl"
                } bg-white border border-zinc-200`}
                onClick={() => handleThumbnailClick(item)}
              >
                {/* Thumbnail Image Area */}
                <div className="relative h-48 bg-gradient-to-br from-zinc-100 to-zinc-200 rounded-t-lg overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className={`p-4 rounded-full ${categoryColors[item.category]}`}>
                      {item.icon}
                    </div>
                  </div>
                  
                  {/* Badges */}
                  <div className="absolute top-3 left-3 flex gap-2">
                    {item.badge && (
                      <Badge variant="secondary" className="bg-white/90 text-zinc-700">
                        {item.badge}
                      </Badge>
                    )}
                    {item.comingSoon && (
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        Coming Soon
                      </Badge>
                    )}
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-3 right-3">
                    <Badge 
                      variant="outline" 
                      className={`${categoryColors[item.category]} text-xs`}
                    >
                      {item.category}
                    </Badge>
                  </div>

                  {/* Hover Overlay */}
                  {!item.comingSoon && (
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-zinc-900 mb-2 group-hover:text-zinc-700 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-zinc-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>

                {/* Coming Soon Overlay */}
                {item.comingSoon && (
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Crown className="w-6 h-6 text-yellow-600" />
                      </div>
                      <p className="font-medium text-zinc-700">Coming Soon</p>
                    </div>
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            {Array.from({ length: Math.ceil(filteredThumbnails.length / 4) }).map((_, index) => (
              <div 
                key={index}
                className="w-2 h-2 rounded-full bg-zinc-300"
              />
            ))}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="max-w-7xl mx-auto px-6 py-8 border-t bg-white/50">
        <div className="grid grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-zinc-900">1,200+</div>
            <div className="text-sm text-zinc-600">Premium Items</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-zinc-900">500+</div>
            <div className="text-sm text-zinc-600">Verified Sellers</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-zinc-900">15,000+</div>
            <div className="text-sm text-zinc-600">Active Members</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-zinc-900">99.8%</div>
            <div className="text-sm text-zinc-600">Satisfaction Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
}
