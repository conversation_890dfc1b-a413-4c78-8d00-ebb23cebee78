"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { ProfileSettings } from "@/components/settings/ProfileSettings";
import { BillingSettings } from "@/components/settings/BillingSettings";
import { ShippingSettings } from "@/components/settings/ShippingSettings";
import { SecuritySettings } from "@/components/settings/SecuritySettings";
import { PreferencesSettings } from "@/components/settings/PreferencesSettings";
import { MemberProfileSettings } from "@/components/settings/MemberProfileSettings";
import { 
  User, 
  CreditCard, 
  Truck, 
  Shield, 
  Bell,
  Users,
  ChevronRight
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";

const settingsNavigation = [
  {
    id: "profile",
    label: "Profile Information",
    description: "Personal details and public profile",
    icon: User,
    component: ProfileSettings
  },
  {
    id: "member-profile",
    label: "Member/Forum Profile",
    description: "Community profile and forum settings",
    icon: Users,
    component: MemberProfileSettings
  },
  {
    id: "billing",
    label: "Billing & Payments",
    description: "Payment methods and billing history",
    icon: CreditCard,
    component: BillingSettings
  },
  {
    id: "shipping",
    label: "Shipping Addresses",
    description: "Delivery addresses and preferences",
    icon: Truck,
    component: ShippingSettings
  },
  {
    id: "security",
    label: "Security & Privacy",
    description: "Password, 2FA, and login sessions",
    icon: Shield,
    component: SecuritySettings
  },
  {
    id: "preferences",
    label: "Notifications & Preferences",
    description: "Email, push notifications, and display settings",
    icon: Bell,
    component: PreferencesSettings
  }
];

interface SettingsPageProps {
  className?: string;
}

export function SettingsPage({ className }: SettingsPageProps) {
  const searchParams = useSearchParams();
  const [activeSection, setActiveSection] = useState("profile");
  
  // Handle URL section parameter
  useEffect(() => {
    const sectionParam = searchParams.get('section');
    if (sectionParam && settingsNavigation.some(nav => nav.id === sectionParam)) {
      setActiveSection(sectionParam);
    }
  }, [searchParams]);
  
  const ActiveComponent = settingsNavigation.find(nav => nav.id === activeSection)?.component || ProfileSettings;
  const activeNav = settingsNavigation.find(nav => nav.id === activeSection);

  return (
    <ProtectedRoute requireAuth={true}>
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
          {/* Header */}
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-black dark:text-white">Account Settings</h1>
              <p className="text-neutral-600 dark:text-neutral-400 mt-1">
                Manage your account preferences and personal information
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <Card className="!p-0">
                <CardContent className="p-1">
                  {settingsNavigation.map((item) => {
                    const Icon = item.icon;
                    const isActive = activeSection === item.id;
                    
                    return (
                      <button
                        key={item.id}
                        onClick={() => setActiveSection(item.id)}
                        className={cn(
                          "w-full text-left p-4 rounded-xl transition-all duration-200 group",
                          isActive 
                            ? "bg-neutral-900 dark:bg-neutral-100 text-white dark:text-neutral-900 shadow-md" 
                            : "hover:bg-neutral-50 dark:hover:bg-neutral-800/50 text-neutral-700 dark:text-neutral-300"
                        )}
                      >
                        <div className="flex items-center space-x-3">
                          <Icon className={cn(
                            "w-5 h-5 transition-colors",
                            isActive 
                              ? "text-white dark:text-neutral-900" 
                              : "text-neutral-500 dark:text-neutral-400"
                          )} />
                          <div className="flex-1 min-w-0">
                            <p className={cn(
                              "font-medium text-sm transition-colors",
                              isActive 
                                ? "text-white dark:text-neutral-900" 
                                : "text-neutral-900 dark:text-neutral-100"
                            )}>
                              {item.label}
                            </p>
                            <p className={cn(
                              "text-xs mt-0.5 transition-colors",
                              isActive 
                                ? "text-neutral-200 dark:text-neutral-600" 
                                : "text-neutral-500 dark:text-neutral-400"
                            )}>
                              {item.description}
                            </p>
                          </div>
                          <ChevronRight className={cn(
                            "w-4 h-4 transition-all duration-200",
                            isActive 
                              ? "text-white dark:text-neutral-900 translate-x-1" 
                              : "text-neutral-400 dark:text-neutral-500 group-hover:translate-x-1"
                          )} />
                        </div>
                      </button>
                    );
                  })}
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <Card className="!p-0">
                <CardHeader className="border-b border-border p-6">
                  <CardTitle className="flex items-center gap-3 text-xl font-medium text-foreground">
                    {activeNav && <activeNav.icon className="w-6 h-6 text-muted-foreground" />}
                    {activeNav?.label}
                  </CardTitle>
                  <CardDescription className="text-muted-foreground">
                    {activeNav?.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-8">
                  <ActiveComponent />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
