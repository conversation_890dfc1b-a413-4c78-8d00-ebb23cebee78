"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Separator } from "@repo/ui/components/separator";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { 
  Building2, 
  Globe, 
  MapPin, 
  Calendar,
  Star,
  MessageSquare,
  ShoppingBag,
  Award,
  Users,
  Link as LinkIcon
} from "lucide-react";
import { ProfileImage } from "@/components/common/ProfileImage";
import { ProductCard } from "@/components/marketplace/ProductCard";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";

interface MemberProfilePageProps {
  memberData: any;
}

export default function MemberProfilePage({ memberData }: MemberProfilePageProps) {
  const { user: currentUser } = useAuth();
  const [referenceContent, setReferenceContent] = useState("");
  const [relationshipType, setRelationshipType] = useState<string>("other");
  const [transactionCount, setTransactionCount] = useState<string>("");
  const [yearsKnown, setYearsKnown] = useState<string>("");

  const addReference = useMutation(api.members.addMemberReference);
  const calculatedActivity = useQuery(api.members.getCalculatedForumActivity, 
    memberData?.user?._id ? { userId: memberData.user._id } : "skip"
  );
  const references = useQuery(api.members.getMemberReferences, 
    memberData?.user?._id ? {
      userId: memberData.user._id,
      paginationOpts: { numItems: 10, cursor: null },
    } : "skip"
  );

  const handleAddReference = async () => {
    if (!referenceContent.trim()) {
      toast.error("Please write a reference");
      return;
    }

    try {
      await addReference({
        toUserId: memberData.user._id,
        content: referenceContent,
        relationshipType: relationshipType as any,
        transactionCount: transactionCount ? parseInt(transactionCount) : undefined,
        yearsKnown: yearsKnown ? parseInt(yearsKnown) : undefined,
      });
      toast.success("Reference added successfully!");
      setReferenceContent("");
      setRelationshipType("other");
      setTransactionCount("");
      setYearsKnown("");
    } catch (error: any) {
      toast.error(error.message || "Failed to add reference");
    }
  };

  // Removed fixProfileCounts function - no longer needed with dynamic calculations

  const { user, memberProfile, sellerProfile, listings, pastListings, referencesCount } = memberData;
  const isOwnProfile = currentUser?._id === user._id;

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Sidebar - Member Info */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center">
                <ProfileImage
                  storageId={user.profileImage}
                  name={user.name}
                  size="xl"
                />
                <h1 className="mt-4 text-2xl font-bold">
                  {memberProfile?.displayName || user.name}
                </h1>
                {user.userType === "seller" && (
                  <Badge className="mt-2" variant="secondary">
                    Verified Seller
                  </Badge>
                )}
                
                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mt-6 w-full">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{listings.length}</div>
                    <div className="text-sm text-muted-foreground">Listings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{pastListings.length}</div>
                    <div className="text-sm text-muted-foreground">Sold</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{referencesCount}</div>
                    <div className="text-sm text-muted-foreground">References</div>
                  </div>
                </div>

                <Separator className="my-6" />

                {/* Member Info */}
                <div className="w-full space-y-3 text-left">
                  {memberProfile?.company && (
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{memberProfile.company}</span>
                    </div>
                  )}
                  {memberProfile?.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{memberProfile.location}</span>
                    </div>
                  )}
                  {memberProfile?.website && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a 
                        href={memberProfile.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline"
                      >
                        {memberProfile.website}
                      </a>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      Member since {format(new Date(memberProfile?.memberSince || Date.now()), "MMM yyyy")}
                    </span>
                  </div>
                </div>

                {/* Specialties */}
                {memberProfile?.specialty && memberProfile.specialty.length > 0 && (
                  <>
                    <Separator className="my-6" />
                    <div className="w-full">
                      <h3 className="text-sm font-semibold mb-2">Specialties</h3>
                      <div className="flex flex-wrap gap-2">
                        {memberProfile.specialty.map((spec: string) => (
                          <Badge key={spec} variant="outline">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* Social Links */}
                {memberProfile?.socialLinks && (
                  <>
                    <Separator className="my-6" />
                    <div className="flex gap-4">
                      {memberProfile.socialLinks.instagram && (
                        <a 
                          href={`https://instagram.com/${memberProfile.socialLinks.instagram}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-foreground"
                        >
                          <LinkIcon className="h-5 w-5" />
                        </a>
                      )}
                      {memberProfile.socialLinks.twitter && (
                        <a 
                          href={`https://twitter.com/${memberProfile.socialLinks.twitter}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-foreground"
                        >
                          <LinkIcon className="h-5 w-5" />
                        </a>
                      )}
                      {memberProfile.socialLinks.linkedin && (
                        <a 
                          href={`https://linkedin.com/in/${memberProfile.socialLinks.linkedin}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-foreground"
                        >
                          <LinkIcon className="h-5 w-5" />
                        </a>
                      )}
                    </div>
                  </>
                )}

                {/* Actions */}
                {!isOwnProfile && (
                  <>
                    <Separator className="my-6" />
                    <div className="w-full space-y-2">
                      <Button className="w-full" variant="outline">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Send Message
                      </Button>
                      {user.userType === "seller" && (
                        <Button className="w-full" variant="outline" asChild>
                          <Link href={`/seller/${user._id}`}>
                            <ShoppingBag className="mr-2 h-4 w-4" />
                            View Store
                          </Link>
                        </Button>
                      )}
                    </div>
                  </>
                )}
                {isOwnProfile && (
                  <>
                    <Separator className="my-6" />
                    <Button className="w-full" variant="outline" asChild>
                      <Link href="/settings?section=member-profile">
                        Edit Profile
                      </Link>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Content - Tabs */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="about" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="about">About</TabsTrigger>
              <TabsTrigger value="listings">Listings</TabsTrigger>
              <TabsTrigger value="past">Past Sales</TabsTrigger>
              <TabsTrigger value="references">References</TabsTrigger>
            </TabsList>

            <TabsContent value="about">
              <Card>
                <CardHeader>
                  <CardTitle>About</CardTitle>
                </CardHeader>
                <CardContent>
                  {memberProfile?.bio ? (
                    <p className="text-muted-foreground">{memberProfile.bio}</p>
                  ) : (
                    <p className="text-muted-foreground">No bio provided yet.</p>
                  )}

                  {memberProfile?.yearsExperience && (
                    <div className="mt-6">
                      <h3 className="font-semibold mb-2">Experience</h3>
                      <p className="text-muted-foreground">
                        {memberProfile.yearsExperience} years in the industry
                      </p>
                    </div>
                  )}

                  {memberProfile?.badges && memberProfile.badges.length > 0 && (
                    <div className="mt-6">
                      <h3 className="font-semibold mb-2">Achievements</h3>
                      <div className="flex flex-wrap gap-2">
                        {memberProfile.badges.map((badge: string) => (
                          <Badge key={badge} variant="secondary">
                            <Award className="mr-1 h-3 w-3" />
                            {badge}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="mt-6 grid grid-cols-3 gap-4">
                    <div className="bg-muted/50 rounded-lg p-4 text-center">
                      <MessageSquare className="h-5 w-5 mx-auto mb-1 text-blue-600" />
                      <div className="text-2xl font-bold">
                        {calculatedActivity === undefined ? "..." : (calculatedActivity?.forumPosts || 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">Forum Posts</div>
                    </div>
                    <div className="bg-muted/50 rounded-lg p-4 text-center">
                      <MessageSquare className="h-5 w-5 mx-auto mb-1 text-green-600" />
                      <div className="text-2xl font-bold">
                        {calculatedActivity === undefined ? "..." : (calculatedActivity?.comments || 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">Comments</div>
                    </div>
                    <div className="bg-muted/50 rounded-lg p-4 text-center">
                      <Star className="h-5 w-5 mx-auto mb-1 text-purple-600" />
                      <div className="text-2xl font-bold">-</div>
                      <div className="text-sm text-muted-foreground">Karma</div>
                      <div className="text-xs text-muted-foreground">Coming soon</div>
                    </div>
                  </div>

                  <div className="mt-4 text-xs text-muted-foreground text-center bg-green-50 dark:bg-green-900/20 p-2 rounded">
                    ✨ Activity stats are calculated in real-time from your actual forum posts and comments
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="listings">
              <Card>
                <CardHeader>
                  <CardTitle>Current Listings</CardTitle>
                </CardHeader>
                <CardContent>
                  {listings.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {listings.map((product: any) => (
                        <ProductCard key={product._id} product={product} />
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No current listings.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="past">
              <Card>
                <CardHeader>
                  <CardTitle>Past Listings</CardTitle>
                </CardHeader>
                <CardContent>
                  {pastListings.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {pastListings.map((product: any) => (
                        <div key={product._id} className="relative">
                          <ProductCard product={product} />
                          <Badge className="absolute top-2 right-2" variant="secondary">
                            Sold
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No past sales.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="references">
              <Card>
                <CardHeader>
                  <CardTitle>References</CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Add Reference Form */}
                  {!isOwnProfile && currentUser && (
                    <div className="mb-6 p-4 border rounded-lg">
                      <h3 className="font-semibold mb-3">Add a Reference</h3>
                      <div className="space-y-3">
                        <Textarea
                          placeholder="Write about your experience with this member..."
                          value={referenceContent}
                          onChange={(e) => setReferenceContent(e.target.value)}
                          rows={4}
                        />
                        <div className="grid grid-cols-3 gap-3">
                          <Select value={relationshipType} onValueChange={setRelationshipType}>
                            <SelectTrigger>
                              <SelectValue placeholder="Relationship" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="buyer">Buyer</SelectItem>
                              <SelectItem value="seller">Seller</SelectItem>
                              <SelectItem value="business_partner">Business Partner</SelectItem>
                              <SelectItem value="colleague">Colleague</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <input
                            type="number"
                            placeholder="Transactions"
                            value={transactionCount}
                            onChange={(e) => setTransactionCount(e.target.value)}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          />
                          <input
                            type="number"
                            placeholder="Years known"
                            value={yearsKnown}
                            onChange={(e) => setYearsKnown(e.target.value)}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                          />
                        </div>
                        <Button onClick={handleAddReference} className="w-full">
                          Submit Reference
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* References List */}
                  <div className="space-y-4">
                    {references?.page && references.page.length > 0 ? (
                      references.page.map((reference: any) => (
                        <div key={reference._id} className="border rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <ProfileImage
                              storageId={reference.fromUser?.profileImage}
                              name={reference.fromUser?.name}
                              size="sm"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Link 
                                  href={`/members/${reference.fromUserId}`}
                                  className="font-semibold hover:underline"
                                >
                                  {reference.fromUser?.name}
                                </Link>
                                {reference.isVerified && (
                                  <Badge variant="secondary" className="text-xs">
                                    Verified
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">
                                {reference.relationshipType.replace("_", " ")} • 
                                {reference.transactionCount && ` ${reference.transactionCount} transactions • `}
                                {reference.yearsKnown && ` Known for ${reference.yearsKnown} years`}
                              </p>
                              <p className="text-sm">{reference.content}</p>
                              <p className="text-xs text-muted-foreground mt-2">
                                {format(new Date(reference.updatedAt), "MMM d, yyyy")}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">No references yet.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
