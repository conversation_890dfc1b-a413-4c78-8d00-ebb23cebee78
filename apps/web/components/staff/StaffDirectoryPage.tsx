"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Users,
  Search,
  Mail,
  Phone,
  Linkedin,
  Calendar,
  Building,
  User
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";

export function StaffDirectoryPage() {
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Queries
  const staff = useQuery(api.staff.getPublicStaff, {
    department: selectedDepartment === "all" ? undefined : selectedDepartment,
  });

  const departments = useQuery(api.staff.getStaffDepartments);

  const filteredStaff = staff?.filter(member => 
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (member.department && member.department.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (member.bio && member.bio.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-light to-white">
      {/* Hero Section */}
      <div className="bg-primary-dark text-primary-light">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-light mb-6">
              Our Team
            </h1>
            <p className="text-xl text-primary-light/80 mb-8">
              Meet the talented individuals who make HauteVault a premier luxury marketplace
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search team members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white"
                />
              </div>
              <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                <SelectTrigger className="w-full sm:w-[200px] bg-white">
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments?.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="h-12 w-12 mx-auto mb-4 text-primary-dark" />
              <div className="text-2xl font-bold mb-2">{staff?.length || 0}</div>
              <p className="text-muted-foreground">Team Members</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Building className="h-12 w-12 mx-auto mb-4 text-primary-dark" />
              <div className="text-2xl font-bold mb-2">{departments?.length || 0}</div>
              <p className="text-muted-foreground">Departments</p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <User className="h-12 w-12 mx-auto mb-4 text-primary-dark" />
              <div className="text-2xl font-bold mb-2">100%</div>
              <p className="text-muted-foreground">Dedicated</p>
            </CardContent>
          </Card>
        </div>

        {/* Filter Results */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h2 className="text-2xl font-semibold">
              Team Members
              {filteredStaff.length > 0 && (
                <span className="text-muted-foreground ml-2">
                  ({filteredStaff.length})
                </span>
              )}
            </h2>
            {selectedDepartment !== "all" && (
              <p className="text-muted-foreground">
                Showing {selectedDepartment} department
              </p>
            )}
          </div>
        </div>

        {/* Staff Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStaff.map((member) => (
            <StaffCard key={member._id} member={member} />
          ))}
        </div>

        {filteredStaff.length === 0 && (
          <div className="text-center py-16">
            <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No team members found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search criteria or browse all departments
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

interface StaffCardProps {
  member: {
    _id: string;
    name: string;
    role: string;
    imageUrl?: string;
    bio?: string;
    department?: string;
    linkedinUrl?: string;
  };
}

function StaffCard({ member }: StaffCardProps) {
  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <CardHeader className="text-center pb-4">
        {/* Profile Image */}
        <div className="w-24 h-24 mx-auto mb-4 relative">
          {member.imageUrl ? (
            <Image
              src={member.imageUrl}
              alt={member.name}
              fill
              className="rounded-full object-cover border-4 border-primary-light"
            />
          ) : (
            <div className="w-full h-full rounded-full bg-gradient-to-br from-primary-dark to-accent-mauve flex items-center justify-center">
              <span className="text-white text-2xl font-semibold">
                {member.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
              </span>
            </div>
          )}
        </div>

        {/* Name and Role */}
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-primary-dark">
            {member.name}
          </h3>
          <p className="text-accent-mauve font-medium">
            {member.role}
          </p>
          {member.department && (
            <Badge variant="outline" className="text-xs">
              {member.department}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Bio */}
        {member.bio && (
          <p className="text-sm text-muted-foreground line-clamp-3">
            {member.bio}
          </p>
        )}

        {/* Contact Actions */}
        <div className="flex justify-center space-x-2 pt-4 border-t">
          {member.linkedinUrl && (
            <Button variant="outline" size="sm" asChild>
              <Link href={member.linkedinUrl} target="_blank">
                <Linkedin className="h-4 w-4" />
              </Link>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
