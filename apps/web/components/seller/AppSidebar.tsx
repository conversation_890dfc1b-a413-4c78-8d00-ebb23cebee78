"use client"

import * as React from "react"
import {
  Package,
  LayoutDashboard,
  TrendingUp,
  BarChart3,
  Settings,
  LogOut,
  User,
  Crown,
  FileText,
  ShoppingBag,
  Plus,
  LifeBuoy,
  Send,
  MessageCircle,
  Building2,
  DollarSign,
} from "lucide-react"
import Link from "next/link"
import { useAuth } from "@/hooks/useBetterAuth"

import { NavMain } from "@/components/seller/NavMain"
import { NavQuickAccess } from "@/components/seller/NavQuickAccess"
import { NavFooter } from "@/components/seller/NavFooter"
import { NavUser } from "@/components/seller/NavUser"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@repo/ui/components/sidebar"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/seller/dashboard",
      icon: LayoutDashboard,
      isActive: true,
    },
    {
      title: "Products",
      url: "/seller/products",
      icon: Package,
      items: [
        {
          title: "All Products",
          url: "/seller/products",
        },
        {
          title: "Unlisted",
          url: "/seller/products?status=unlisted",
        },
        {
          title: "Listed",
          url: "/seller/products?status=listed",
        },
        {
          title: "Sold",
          url: "/seller/products?status=sold",
        },
        {
          title: "Processed",
          url: "/seller/products?status=processed",
        },
        {
          title: "Add Product",
          url: "/seller/products/new",
        },
      ],
    },
    {
      title: "Suppliers",
      url: "/seller/suppliers",
      icon: Building2,
      items: [
        {
          title: "Dashboard",
          url: "/seller/suppliers",
        },
        {
          title: "All Suppliers",
          url: "/seller/suppliers/list",
        },
        {
          title: "Active",
          url: "/seller/suppliers/list?status=active",
        },
        {
          title: "Inactive",
          url: "/seller/suppliers/list?status=inactive",
        },
        {
          title: "Add Supplier",
          url: "/seller/suppliers/new",
        },
      ],
    },
    {
      title: "Offers",
      url: "/seller/offers",
      icon: DollarSign,
      items: [
        {
          title: "Dashboard",
          url: "/seller/offers",
        },
        {
          title: "All Offers",
          url: "/seller/offers/list",
        },
        {
          title: "Pending",
          url: "/seller/offers/list?status=pending",
        },
        {
          title: "Countered",
          url: "/seller/offers/list?status=countered",
        },
        {
          title: "Accepted",
          url: "/seller/offers/list?status=accepted",
        },
        {
          title: "Declined",
          url: "/seller/offers/list?status=declined",
        },
      ],
    },
    {
      title: "Sales",
      url: "/seller/sales",
      icon: TrendingUp,
    },
    {
      title: "Invoices",
      url: "/seller/invoices",
      icon: FileText,
      items: [
        {
          title: "All Invoices",
          url: "/seller/invoices",
        },
        {
          title: "Draft",
          url: "/seller/invoices?status=draft",
        },
        {
          title: "Sent",
          url: "/seller/invoices?status=sent",
        },
        {
          title: "Paid",
          url: "/seller/invoices?status=paid",
        },
        {
          title: "Overdue",
          url: "/seller/invoices?status=overdue",
        },
        {
          title: "Cancelled",
          url: "/seller/invoices?status=cancelled",
        },
      ],
    },
    {
      title: "Messages",
      url: "/seller/messages",
      icon: MessageCircle,
    },
    // {
    //   title: "Reports",
    //   url: "/seller/reports",
    //   icon: BarChart3,
    //   items: [
    //     {
    //       title: "Sales Reports",
    //       url: "/seller/reports",
    //     },
    //     {
    //       title: "Performance",
    //       url: "/seller/reports/performance",
    //     },
    //     {
    //       title: "Analytics",
    //       url: "/seller/reports/analytics",
    //     },
    //   ],
    // },
  ],
  navFooter: [
    {
      title: "Support",
      url: "/help",
      icon: LifeBuoy,
    },
    {
      title: "Feedback",
      url: "/feedback",
      icon: Send,
    },
  ],
  quickAccess: [
    {
      name: "Settings",
      url: "/seller/settings",
      icon: Settings,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth()

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/seller/dashboard">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Package className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">HauteVault</span>
                  <span className="truncate text-xs">Seller Portal</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavQuickAccess items={data.quickAccess} />
        <NavFooter items={data.navFooter} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  )
}
