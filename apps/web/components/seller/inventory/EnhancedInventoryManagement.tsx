"use client";

import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { useRouter, useSearchPara<PERSON>, usePathname } from "next/navigation";
import Image from "next/image";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@repo/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { BulkUploadModal } from "./BulkUploadModal";
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Archive,
  Star,
  TrendingUp,
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Filter,
  Upload,
  Grid,
  List,
  Settings,
  Crown,
  ShoppingBag,
  Heart,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";

interface FilterState {
  search: string;
  status: "all" | "unlisted" | "listed" | "sold" | "processed";
  sortBy: "newest" | "oldest" | "price_high" | "price_low" | "views" | "status";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  category: string;
  condition: string;
  priceRange: { min: string; max: string };
  brand: string;
}

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  sold: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300", 
  draft: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  archived: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

const ITEMS_PER_PAGE_OPTIONS = [25, 50, 100, 200];

export function EnhancedInventoryManagement() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Initialize filters from URL params
  const initializeFiltersFromURL = useCallback((): FilterState => {
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const category = searchParams.get('category') || 'all';
    const condition = searchParams.get('condition') || 'all';
    const brand = searchParams.get('brand') || 'all';
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    return {
      search,
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit: ITEMS_PER_PAGE_OPTIONS.includes(limit) ? limit : 50,
      offset: Math.max(0, offset),
      category,
      condition,
      priceRange: { min: "", max: "" },
      brand,
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<FilterState>(() => {
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const category = searchParams.get('category') || 'all';
    const condition = searchParams.get('condition') || 'all';
    const brand = searchParams.get('brand') || 'all';
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    return {
      search,
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit: ITEMS_PER_PAGE_OPTIONS.includes(limit) ? limit : 50,
      offset: Math.max(0, offset),
      category,
      condition,
      priceRange: { min: "", max: "" },
      brand,
    };
  });
  const [searchQuery, setSearchQuery] = useState(filters.search);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(filters.search);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Use ref to track if we're initializing to prevent loops
  const isInitializing = useRef(true);
  
  // Ref for search input to auto-focus
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Update filters when URL changes
  useEffect(() => {
    const newFilters = initializeFiltersFromURL();
    setFilters(newFilters);
    
    // Update search states only if different (handles browser navigation)
    if (newFilters.search !== searchQuery) {
      setSearchQuery(newFilters.search);
      setDebouncedSearchQuery(newFilters.search);
    }
    
    // Mark as no longer initializing after first run
    isInitializing.current = false;
  }, [searchParams]); // Only depend on searchParams, not initializeFiltersFromURL

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Update URL and filters when debounced search changes
  useEffect(() => {
    // Skip if we're still initializing or if search hasn't actually changed
    if (isInitializing.current || debouncedSearchQuery === filters.search) {
      return;
    }

    const newFilters = { ...filters, search: debouncedSearchQuery, offset: 0 };
    setFilters(newFilters);
    updateURL(newFilters);
  }, [debouncedSearchQuery]); // Only depend on debouncedSearchQuery

  // Update URL when filters change
  const updateURL = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();
    
    if (newFilters.status && newFilters.status !== 'all') {
      params.set('status', newFilters.status);
    }
    if (newFilters.search) {
      params.set('search', newFilters.search);
    }
    if (newFilters.sortBy && newFilters.sortBy !== 'newest') {
      params.set('sortBy', newFilters.sortBy);
    }
    if (newFilters.category && newFilters.category !== 'all') {
      params.set('category', newFilters.category);
    }
    if (newFilters.condition && newFilters.condition !== 'all') {
      params.set('condition', newFilters.condition);
    }
    if (newFilters.brand && newFilters.brand !== 'all') {
      params.set('brand', newFilters.brand);
    }
    if (newFilters.viewMode && newFilters.viewMode !== 'table') {
      params.set('viewMode', newFilters.viewMode);
    }
    if (newFilters.limit !== 50) {
      params.set('limit', newFilters.limit.toString());
    }
    if (newFilters.offset > 0) {
      params.set('offset', newFilters.offset.toString());
    }
    
    const newURL = params.toString() ? `${pathname}?${params.toString()}` : pathname;
    
    // Use window.history.replaceState instead of router.replace to avoid re-renders
    if (window.location.pathname + window.location.search !== newURL) {
      window.history.replaceState({}, '', newURL);
    }
  }, [pathname]);

  // Fetch inventory data - use debouncedSearchQuery for API calls
  const getStatusForApi = (status: FilterState["status"]) => {
    switch (status) {
      case "all": return undefined;
      case "listed": return "active" as const;
      case "unlisted": return "draft" as const;
      case "processed": return "archived" as const;
      case "sold": return "sold" as const;
      default: return undefined;
    }
  };

  const inventoryData = useQuery(api.productQueries.getSellerInventory, {
    status: getStatusForApi(filters.status),
    searchQuery: debouncedSearchQuery || undefined, // Use debouncedSearchQuery
    sortBy: filters.sortBy,
    limit: filters.limit,
    offset: filters.offset,
    includeMetrics: true,
  });

  // Auto-focus search input when component mounts and after data refreshes
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [inventoryData]); // Re-focus when inventory data changes

  // Refresh inventory data after bulk upload
  const handleBulkUploadSuccess = useCallback(() => {
    // The query will automatically refresh due to Convex's reactivity
    toast.success("Inventory refreshed after bulk upload");
  }, []);

  // Mutations for product actions
  const removeProduct = useMutation(api.productManagement.removeProduct);
  const publishProduct = useMutation(api.productManagement.publishProduct);
  const updateProduct = useMutation(api.productManagement.updateProduct);

  const formatCurrency = (amount: number) => {
    if (amount >= 100000) {
      return `$${(amount / 100000).toFixed(1)}L`;
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    if (filters[key] === value) {
      return;
    }
    
    // Handle search separately
    if (key === 'search') {
      setSearchQuery(value);
      return;
    }
    
    const newFilters = {
      ...filters,
      [key]: value,
      offset: key !== 'offset' ? 0 : value,
    };
    
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked && inventoryData?.products) {
      setSelectedItems(inventoryData.products.map((product: any) => product._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case "archive":
          for (const productId of selectedItems) {
            await removeProduct({ 
              productId: productId as any,
              reason: "Bulk archive operation"
            });
          }
          toast.success(`Successfully archived ${selectedItems.length} products`);
          break;
        case "feature":
          for (const productId of selectedItems) {
            await updateProduct({ 
              productId: productId as any
            });
          }
          toast.success(`Successfully featured ${selectedItems.length} products`);
          break;
        case "delete":
          for (const productId of selectedItems) {
            await removeProduct({ 
              productId: productId as any,
              reason: "Bulk delete operation"
            });
          }
          toast.success(`Successfully deleted ${selectedItems.length} products`);
          break;
      }
      setSelectedItems([]);
    } catch (error) {
      toast.error(`Failed to perform bulk action: ${error}`);
    }
  };

  const handleProductAction = async (productId: string, action: string) => {
    try {
      switch (action) {
        case "edit":
          router.push(`/seller/products/${productId}/edit`);
          break;
        case "view":
          router.push(`/marketplace/product/${productId}`);
          break;
        case "archive":
          // This will be handled by the AlertDialog confirmation
          await removeProduct({ 
            productId: productId as any,
            reason: "Manual archive"
          });
          toast.success("Product archived successfully");
          break;
        case "feature":
          await updateProduct({ 
            productId: productId as any
          });
          toast.success("Product featured successfully");
          break;
        case "publish":
          await publishProduct({ productId: productId as any });
          toast.success("Product published successfully");
          break;
        case "delete":
          await removeProduct({ 
            productId: productId as any,
            reason: "Manual deletion"
          });
          toast.success("Product deleted successfully");
          break;
        case "sell_offline":
          router.push(`/seller/products/${productId}/sell-offline`);
          break;
      }
    } catch (error) {
      toast.error(`Failed to perform action: ${error}`);
    }
  };

  // Pagination handlers
  const handlePageChange = (newOffset: number) => {
    handleFilterChange('offset', newOffset);
  };

  const handleItemsPerPageChange = (newLimit: number) => {
    handleFilterChange('limit', newLimit);
  };

  const goToFirstPage = () => handlePageChange(0);
  const goToPreviousPage = () => handlePageChange(Math.max(0, filters.offset - filters.limit));
  const goToNextPage = () => handlePageChange(filters.offset + filters.limit);
  const goToLastPage = () => {
    if (inventoryData?.pagination) {
      const lastOffset = Math.floor(inventoryData.pagination.total / filters.limit) * filters.limit;
      handlePageChange(lastOffset);
    }
  };

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    if (!inventoryData?.summary) return null;
    
    return {
      totalProducts: inventoryData.summary.total || 0,
      activeProducts: inventoryData.summary.byStatus?.active || 0,
      totalValue: inventoryData.summary.totalValue || 0,
      avgPrice: inventoryData.summary.averagePrice || 0,
    };
  }, [inventoryData]);

  // Pagination info
  const paginationInfo = useMemo(() => {
    if (!inventoryData?.pagination) return null;
    
    const { total, limit, offset, hasMore } = inventoryData.pagination;
    const currentPage = Math.floor(offset / limit) + 1;
    const totalPages = Math.ceil(total / limit);
    const startItem = offset + 1;
    const endItem = Math.min(offset + limit, total);
    
    return {
      currentPage,
      totalPages,
      startItem,
      endItem,
      total,
      hasMore,
      hasPrevious: offset > 0,
    };
  }, [inventoryData]);

  if (inventoryData === undefined) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading inventory...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background">
      {/* Full Width Header with Search and Actions */}
      {/* THIS SECTION SHOULD NOT REFRESH ON FILTERS */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery} // Use searchQuery instead of localSearch
                  onChange={(e) => {
                    const value = e.target.value;
                    setSearchQuery(value); // Update search state immediately
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      // Immediately trigger search on Enter
                      setDebouncedSearchQuery(searchQuery);
                    }
                  }}
                  onBlur={() => {
                    // Immediately trigger search on blur
                    setDebouncedSearchQuery(searchQuery);
                  }}
                  className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              {/* Status Dropdown */}
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value as FilterState["status"])}>
                <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Grid className="w-4 h-4" />
                      ALL
                    </div>
                  </SelectItem>
                  <SelectItem value="unlisted" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      UNLISTED
                    </div>
                  </SelectItem>
                  <SelectItem value="listed" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      LISTED
                    </div>
                  </SelectItem>
                  <SelectItem value="sold" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Crown className="w-4 h-4" />
                      SOLD
                    </div>
                  </SelectItem>
                  <SelectItem value="processed" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="w-4 h-4" />
                      PROCESSED
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Filter Popover */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="rounded-xl font-light"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-6" align="start">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                    
                    {/* Category Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Category</label>
                      <Select value={filters.category} onValueChange={(value) => handleFilterChange("category", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Categories" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="clothing">Clothing</SelectItem>
                          <SelectItem value="sneakers">Sneakers</SelectItem>
                          <SelectItem value="handbags">Handbags</SelectItem>
                          <SelectItem value="accessories">Accessories</SelectItem>
                          <SelectItem value="watches">Watches</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Condition Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Condition</label>
                      <Select value={filters.condition} onValueChange={(value) => handleFilterChange("condition", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Conditions" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Conditions</SelectItem>
                          <SelectItem value="new">New</SelectItem>
                          <SelectItem value="excellent">Excellent</SelectItem>
                          <SelectItem value="good">Good</SelectItem>
                          <SelectItem value="fair">Fair</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Price Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Price Range</label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Min"
                          value={filters.priceRange.min}
                          onChange={(e) => handleFilterChange("priceRange", { ...filters.priceRange, min: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          placeholder="Max"
                          value={filters.priceRange.max}
                          onChange={(e) => handleFilterChange("priceRange", { ...filters.priceRange, max: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                    <Button 
                      onClick={() => {
                        const newFilters = { ...filters, category: "all", condition: "all", priceRange: { min: "", max: "" }, brand: "all" };
                        setFilters(newFilters);
                        updateURL(newFilters);
                      }}
                      variant="outline" 
                      className="w-full rounded-xl font-light"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <BulkUploadModal onSuccess={handleBulkUploadSuccess} />
              
              <Button 
                variant="ghost" 
                className="text-accent hover:text-accent/80 font-light rounded-xl px-4 py-2 transition-all duration-300"
              >
                <Crown className="w-4 h-4 mr-2" />
                CONSIGN
              </Button>
              
              <Button 
                onClick={() => window.location.href = "/seller/products/new"}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                ADD ITEM
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* EVERYTHING BELOW SHOULD REFRESH ON FILTERS */}
      {/* Full Width Table */}
      <div className="flex-1">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedItems.length === inventoryData?.products?.length}
                  onCheckedChange={handleSelectAll}
                  className="rounded border-border"
                />
              </TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Product</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">SKU/Style ID</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Size</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Condition</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Buy Price</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">List Price</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Acquired Date</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase text-center">Views</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase text-center">Favorites</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Status</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {inventoryData?.products && inventoryData.products.length > 0 ? (
              inventoryData.products.map((product: any) => (
                <TableRow key={product._id} className="hover:bg-primary/5 transition-colors duration-300 max-w-fit">
                  <TableCell>
                    <Checkbox 
                      checked={selectedItems.includes(product._id)}
                      onCheckedChange={(checked) => handleSelectItem(product._id, checked as boolean)}
                      className="rounded border-border"
                    />
                  </TableCell>
                  <TableCell className="w-40 max-w-[8rem]">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-primary/5 rounded-lg overflow-hidden border border-border flex-shrink-0">
                        {product.images && product.images[0] ? (
                          <Image
                            src={product.images[0]}
                            alt={product.title}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-primary/5 flex items-center justify-center">
                            <Package className="w-3 h-3 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-light text-foreground text-xs truncate">{product.title}</p>
                        <p className="text-muted-foreground text-xs font-light truncate">{product.brand}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">{product._id.slice(-8)}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{product.size || "—"}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{product.condition}</TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(product.price * 0.7)}</TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(product.price)}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{formatDate(product._creationTime)}</TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1 text-muted-foreground">
                      <Eye className="w-4 h-4" />
                      <span className="font-light">{product.views || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1 text-muted-foreground">
                      <Heart className="w-4 h-4" />
                      <span className="font-light">{product.saves || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${STATUS_COLORS[product.status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800'}`}
                    >
                      {product.status === 'active' ? 'Listed' : 
                       product.status === 'draft' ? 'Draft' :
                       product.status === 'sold' ? 'Sold' :
                       product.status === 'archived' ? 'Archived' :
                       product.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleProductAction(product._id, "edit")}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleProductAction(product._id, "view")}>
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {product.status === 'draft' && (
                          <DropdownMenuItem onClick={() => handleProductAction(product._id, "publish")}>
                            <Eye className="w-4 h-4 mr-2" />
                            Publish
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleProductAction(product._id, "sell_offline")}>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Sell Offline
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                              <Archive className="w-4 h-4 mr-2" />
                              Archive
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Archive Product</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to archive this product? This will remove it from the marketplace.
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleProductAction(product._id, "archive")}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Archive Product
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={12} className="text-center py-16">
                  <div className="flex flex-col items-center justify-center">
                    <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mb-4 border border-border">
                      <Package className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-light text-primary mb-2">
                      We could not find any items matching your criteria
                    </h3>
                    <p className="text-muted-foreground mb-6 text-center max-w-md font-light">
                      Select the "Add Item" button below to start filling your inventory with amazing products.
                    </p>
                    <Button 
                      onClick={() => window.location.href = "/seller/products/new"}
                      className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl font-light transition-all duration-300"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      ADD ITEM
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {paginationInfo && paginationInfo.total > 0 && (
        <div className="border-t border-border bg-card">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Items per page selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-light text-muted-foreground">Show</span>
                <Select value={filters.limit.toString()} onValueChange={(value) => handleItemsPerPageChange(parseInt(value))}>
                  <SelectTrigger className="w-20 h-8 rounded-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ITEMS_PER_PAGE_OPTIONS.map(option => (
                      <SelectItem key={option} value={option.toString()}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm font-light text-muted-foreground">items per page</span>
              </div>

              {/* Pagination info */}
              <div className="text-sm font-light text-muted-foreground">
                {paginationInfo.startItem}–{paginationInfo.endItem} of {paginationInfo.total} items
              </div>

              {/* Pagination navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
                    let pageNum;
                    if (paginationInfo.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage >= paginationInfo.totalPages - 2) {
                      pageNum = paginationInfo.totalPages - 4 + i;
                    } else {
                      pageNum = paginationInfo.currentPage - 2 + i;
                    }
                    
                    if (pageNum < 1 || pageNum > paginationInfo.totalPages) return null;
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === paginationInfo.currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange((pageNum - 1) * filters.limit)}
                        className="h-8 w-8 p-0 rounded-lg text-sm"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={!paginationInfo.hasMore}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={!paginationInfo.hasMore}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Footer Stats */}
      <div className="border-t border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between text-sm font-light">
            <div className="text-primary">
              Total List Price: <span className="font-medium">{formatCurrency(summaryStats?.totalValue || 0)}</span>
            </div>
            <div className="text-primary">
              Total Cost: <span className="font-medium">{formatCurrency((summaryStats?.totalValue || 0) * 0.7)}</span>
            </div>
            <div className="text-muted-foreground">
              {paginationInfo ? `${paginationInfo.startItem}–${paginationInfo.endItem} of ${paginationInfo.total}` : '0–0 of 0'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
