"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { BulkUploadModal } from "./BulkUploadModal";
import { 
  Plus,
  Search,
  Filter,
  Grid,
  Package,
  Eye,
  Crown,
  ShoppingBag
} from "lucide-react";

interface FilterState {
  search: string;
  status: "all" | "unlisted" | "listed" | "sold" | "processed";
  sortBy: "newest" | "oldest" | "price_high" | "price_low" | "views" | "status";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  category: string;
  condition: string;
  priceRange: { min: string; max: string };
  brand: string;
}

interface InventoryHeaderProps {
  filters: FilterState;
  onFilterChange: (key: keyof FilterState, value: any) => void;
  onBulkUploadSuccess: () => void;
}

export function InventoryHeader({ 
  filters, 
  onFilterChange, 
  onBulkUploadSuccess 
}: InventoryHeaderProps) {
  const [localSearchQuery, setLocalSearchQuery] = useState(filters.search);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Update local search when filters change (e.g., from URL navigation)
  useEffect(() => {
    if (filters.search !== localSearchQuery) {
      setLocalSearchQuery(filters.search);
    }
  }, [filters.search]);

  const handleSearchChange = (value: string) => {
    setLocalSearchQuery(value);
    // Only update filters after a delay to avoid constant re-renders
    const timeoutId = setTimeout(() => {
      onFilterChange('search', value);
    }, 300);
    return () => clearTimeout(timeoutId);
  };

  const handleSearchSubmit = () => {
    onFilterChange('search', localSearchQuery);
  };

  return (
    <div className="border-b border-border bg-card">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between gap-4">
          {/* Search and Filters */}
          <div className="flex items-center gap-4 flex-1">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                type="text"
                placeholder="Search products..."
                value={localSearchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearchSubmit();
                  }
                }}
                onBlur={handleSearchSubmit}
                className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
              />
            </div>
            
            {/* Status Dropdown */}
            <Select value={filters.status} onValueChange={(value) => onFilterChange("status", value as FilterState["status"])}>
              <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" className="rounded-xl font-light">
                  <div className="flex items-center gap-2">
                    <Grid className="w-4 h-4" />
                    ALL
                  </div>
                </SelectItem>
                <SelectItem value="unlisted" className="rounded-xl font-light">
                  <div className="flex items-center gap-2">
                    <Package className="w-4 h-4" />
                    UNLISTED
                  </div>
                </SelectItem>
                <SelectItem value="listed" className="rounded-xl font-light">
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4" />
                    LISTED
                  </div>
                </SelectItem>
                <SelectItem value="sold" className="rounded-xl font-light">
                  <div className="flex items-center gap-2">
                    <Crown className="w-4 h-4" />
                    SOLD
                  </div>
                </SelectItem>
                <SelectItem value="processed" className="rounded-xl font-light">
                  <div className="flex items-center gap-2">
                    <ShoppingBag className="w-4 h-4" />
                    PROCESSED
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Filter Popover */}
            <Popover>
              <PopoverTrigger asChild>
                <Button 
                  variant="outline" 
                  className="rounded-xl font-light"
                >
                  <Filter className="w-4 h-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-6" align="start">
                <div className="space-y-4">
                  <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                  
                  {/* Category Filter */}
                  <div>
                    <label className="text-sm font-light text-muted-foreground mb-2 block">Category</label>
                    <Select value={filters.category} onValueChange={(value) => onFilterChange("category", value)}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="All Categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="clothing">Clothing</SelectItem>
                        <SelectItem value="sneakers">Sneakers</SelectItem>
                        <SelectItem value="handbags">Handbags</SelectItem>
                        <SelectItem value="accessories">Accessories</SelectItem>
                        <SelectItem value="watches">Watches</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Condition Filter */}
                  <div>
                    <label className="text-sm font-light text-muted-foreground mb-2 block">Condition</label>
                    <Select value={filters.condition} onValueChange={(value) => onFilterChange("condition", value)}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="All Conditions" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Conditions</SelectItem>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Price Range */}
                  <div>
                    <label className="text-sm font-light text-muted-foreground mb-2 block">Price Range</label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Min"
                        value={filters.priceRange.min}
                        onChange={(e) => onFilterChange("priceRange", { ...filters.priceRange, min: e.target.value })}
                        className="rounded-xl"
                      />
                      <Input
                        placeholder="Max"
                        value={filters.priceRange.max}
                        onChange={(e) => onFilterChange("priceRange", { ...filters.priceRange, max: e.target.value })}
                        className="rounded-xl"
                      />
                    </div>
                  </div>

                  <Button 
                    onClick={() => {
                      onFilterChange("category", "all");
                      onFilterChange("condition", "all");
                      onFilterChange("priceRange", { min: "", max: "" });
                      onFilterChange("brand", "all");
                    }}
                    variant="outline" 
                    className="w-full rounded-xl font-light"
                  >
                    Clear Filters
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-4">
            <BulkUploadModal onSuccess={onBulkUploadSuccess} />
            
            <Button 
              variant="ghost" 
              className="text-accent hover:text-accent/80 font-light rounded-xl px-4 py-2 transition-all duration-300"
            >
              <Crown className="w-4 h-4 mr-2" />
              CONSIGN
            </Button>
            
            <Button 
              onClick={() => window.location.href = "/seller/products/new"}
              className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl px-6 py-2 transition-all duration-300"
            >
              <Plus className="w-4 h-4 mr-2" />
              ADD ITEM
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
