"use client";

import { useState, useMemo, use<PERSON><PERSON>back, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { DashboardLoader } from "@/components/common";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { Bar, BarChart, CartesianGrid, XAxis } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@repo/ui/components/chart";
import { 
  ShoppingCart, 
  DollarSign, 
  Calculator,
  BarChart3,
  Building2
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { DashboardFiltersHeader } from "./DashboardFiltersHeader";
import { usePersistentFilters, type DashboardFilters } from "@/hooks/usePersistentFilters";
import { SellerApplicationModal } from "@/components/seller/SellerApplicationModal";
import { toast } from "sonner";

export function ModernSellerDashboard() {
  const { user } = useAuth();
  const [activeChart, setActiveChart] = useState<"owned" | "consigned">("owned");
  const [activeFilter, setActiveFilter] = useState<"all" | "unlisted" | "listed">("all");
  
  // Use persistent filters hook
  const { filters, updateFilters, clearFilters, isLoaded } = usePersistentFilters();
  
  // Track when content is refreshing due to filter changes
  const [isFilteredDataLoading, setIsFilteredDataLoading] = useState(false);

  // Mutation for creating seller applications
  const submitSellerApplication = useMutation(api.sellerApplicationsSimple.submitApplication);
  const updateUserProfile = useMutation(api.auth.updateUserProfile);

  // Check for pending seller application on component mount
  useEffect(() => {
    const checkPendingSellerApplication = async () => {
      try {
        const pendingData = sessionStorage.getItem('pendingSellerApplication');
        if (pendingData && user && user._id) {
          const applicationData = JSON.parse(pendingData);
          
          // Check if this is recent (within last 5 minutes)
          const isRecent = Date.now() - applicationData.timestamp < 5 * 60 * 1000;
          
          if (isRecent) {
            console.log("Found pending seller application, creating it now:", applicationData);
            
            const applicationPayload = {
              firstName: applicationData.firstName,
              lastName: applicationData.lastName,
              email: applicationData.email,
              phone: applicationData.phone,
              dateOfBirth: "N/A",
              businessName: applicationData.company,
              businessType: "individual" as const,
              taxId: "N/A",
              businessAddress: "N/A",
              businessCity: "N/A",
              businessState: "N/A",
              businessZip: "N/A",
              businessCountry: "N/A",
              yearsExperience: "1",
              previousPlatforms: [],
              monthlyVolume: applicationData.numberOfProducts,
              specialties: applicationData.productSpecialty ? [applicationData.productSpecialty] : [],
              termsAccepted: true,
              privacyAccepted: true,
            };
            
            const applicationId = await submitSellerApplication(applicationPayload);
            
            // Update user type to seller
            try {
              await updateUserProfile({
                userType: "seller",
              });
              console.log("User type updated to seller");
            } catch (profileError) {
              console.error("Failed to update user type:", profileError);
              // Don't fail the whole process if profile update fails
            }
            
            // Clear the pending data
            sessionStorage.removeItem('pendingSellerApplication');
            
            console.log("Seller application created successfully from pending data, ID:", applicationId);
            toast.success("Seller application submitted successfully!", {
              description: "Your application has been created and is under review.",
            });
          } else {
            // Clear old data
            sessionStorage.removeItem('pendingSellerApplication');
          }
        }
      } catch (error) {
        console.error("Failed to process pending seller application:", error);
        // Clear the pending data on error
        sessionStorage.removeItem('pendingSellerApplication');
        toast.error("Failed to submit seller application", {
          description: "Please complete your application manually.",
        });
      }
    };
    
    // Only run if user is authenticated
    if (user && user._id) {
      checkPendingSellerApplication();
    }
  }, [user, submitSellerApplication, updateUserProfile]);

  // Convert date strings to timestamps for backend
  const backendFilters = useMemo(() => {
    const convertedFilters: any = {};
    
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'soldStartDate' && value) {
        convertedFilters.soldStartDate = new Date(value).getTime();
      } else if (key === 'soldEndDate' && value) {
        convertedFilters.soldEndDate = new Date(value).getTime();
      } else if (value && value !== '' && value !== 'all') {
        convertedFilters[key] = value;
      }
    });
  
    
    return convertedFilters;
  }, [filters]);

  // Fetch filtered dashboard data - only when filters are loaded
  const dashboardData = useQuery(
    api.sellerQueries.getFilteredSellerDashboard, 
    isLoaded ? { filters: backendFilters } : "skip"
  );
  
  const sellerMetrics = useQuery(api.sellerQueries.getSellerMetrics, { 
    timeRange: "30d"
  });

  const inventoryData = useQuery(api.productQueries.getSellerInventory, {
    status: undefined,
    limit: 1000,
    includeMetrics: true
  });

  // Fetch supplier summary for dashboard
  const supplierSummary = useQuery(
    api.supplierManagement.getSupplierSummary, 
    user?._id ? { sellerId: user.userId } : "skip"
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Handle filter changes with persistence
  const handleFiltersChange = useCallback((newFilters: Partial<DashboardFilters>) => {
    setIsFilteredDataLoading(true);
    updateFilters(newFilters);
  }, [updateFilters]);

  // Handle clearing filters with persistence
  const handleClearFilters = useCallback(() => {
    clearFilters();
  }, [clearFilters]);

  // Calculate real metrics from inventory data
  const metrics = useMemo(() => {
    // Calculate from real inventory data first
    if (inventoryData?.products && inventoryData?.summary) {
      const products = inventoryData.products;
      const summary = inventoryData.summary;
      
      // Calculate real inventory value from actual product prices
      // Only include products that haven't been sold (active or draft status)
      const realInventoryValue = products
        .filter((p: any) => p.status === "active" || p.status === "draft")
        .reduce((sum: any, product: any) => sum + product.price, 0);

      const totalProducts = summary.total || 0;
      const activeProducts = summary.byStatus?.active || 0;
      const draftProducts = summary.byStatus?.draft || 0;
      const soldProducts = summary.byStatus?.sold || 0;

      // Calculate owned vs consigned from actual product data
      // Only count products that haven't been sold (active or draft status)
      const ownedProducts = products.filter((p: any) => 
        (p.ownershipType === "owned" || !p.ownershipType) && // Default to owned if not set
        (p.status === "active" || p.status === "draft") // Only unsold products
      ).length;
      const consignedProducts = products.filter((p: any) => 
        p.ownershipType === "consigned" &&
        (p.status === "active" || p.status === "draft") // Only unsold products
      ).length;

      return {
        totalSales: soldProducts,
        totalRevenue: sellerMetrics?.revenue?.total || 0,
        totalCost: realInventoryValue * 0.7, // Calculate cost from unsold inventory only (70% of list price)
        grossProfit: sellerMetrics?.revenue?.total || 0, // Revenue from sales
        averageGrossMargin: (sellerMetrics?.revenue?.total || 0) > 0 ? 30 : 0, // 30% margin (100% - 70% cost)
        averageTransactionValue: sellerMetrics?.performance?.averageOrderValue || 0,
        inventoryValue: realInventoryValue,
        activeProducts: activeProducts,
        totalProducts: totalProducts,
        ownedProducts: ownedProducts,
        consignedProducts: consignedProducts,
      };
    }

    // Use dashboard data if available and valid, but recalculate costs from unsold inventory
    if (dashboardData?.metrics && dashboardData.metrics.inventoryValue > 0) {
      const unsoldInventoryValue = dashboardData.metrics.inventoryValue;
      return {
        ...dashboardData.metrics,
        totalCost: unsoldInventoryValue * 0.7, // Recalculate cost from unsold inventory only
        grossProfit: sellerMetrics?.revenue?.total || 0, // Revenue from sales
        averageGrossMargin: (sellerMetrics?.revenue?.total || 0) > 0 ? 30 : 0, // 30% margin
      };
    }

    // Fallback to sellerMetrics if available
    if (sellerMetrics) {
      const fallbackInventoryValue = sellerMetrics.products?.total || 0;
      return {
        totalSales: sellerMetrics.revenue?.total || 0,
        totalRevenue: sellerMetrics.revenue?.total || 0,
        totalCost: fallbackInventoryValue * 0.7, // Calculate cost from inventory only (70% of list price)
        grossProfit: sellerMetrics.revenue?.total || 0, // Revenue from sales
        averageGrossMargin: (sellerMetrics.revenue?.total || 0) > 0 ? 30 : 0, // 30% margin
        averageTransactionValue: sellerMetrics.performance?.averageOrderValue || 0,
        inventoryValue: fallbackInventoryValue,
        ownedProducts: sellerMetrics.products?.total || 0,
        consignedProducts: 0,
        activeProducts: sellerMetrics.products?.active || 0,
      };
    }

    return {
      totalSales: 0,
      totalRevenue: 0,
      totalCost: 0,
      grossProfit: 0,
      averageGrossMargin: 0,
      averageTransactionValue: 0,
      inventoryValue: 0,
      ownedProducts: 0,
      consignedProducts: 0,
      activeProducts: 0,
    };
  }, [inventoryData, dashboardData?.metrics, sellerMetrics]);

  // Chart configuration
  const chartConfig = {
    owned: {
      label: "Owned",
      color: "hsl(var(--primary))",
    },
    consigned: {
      label: "Consigned", 
      color: "hsl(var(--accent))",
    },
  };

  // Generate chart data from real inventory showing only days with product additions
  const chartData = useMemo(() => {
    
    
    // Bypass cached dashboard data to force our custom logic
    // if (dashboardData?.chartData?.inventory && dashboardData.chartData.inventory.length > 0) {
    //   console.log("📊 Using dashboard data:", dashboardData.chartData.inventory);
    //   return dashboardData.chartData.inventory;
    // }

    
    
    // Generate real chart data from current inventory showing only days with product additions
    if (inventoryData?.products && inventoryData.products.length > 0) {
      // Log the actual product objects
      
      
      // Filter products based on active filter
      let filteredProducts = inventoryData.products;
      
      if (activeFilter === "listed") {
        filteredProducts = inventoryData.products.filter((p: any) => p.status === "active");
      } else if (activeFilter === "unlisted") {
        filteredProducts = inventoryData.products.filter((p: any) => p.status === "draft");
      }
      // "all" shows all products that haven't been sold
      
      // Only count products that haven't been sold (active or draft status)
      const unsoldProducts = filteredProducts.filter((p: any) => 
        p.status === "active" || p.status === "draft"
      );
      
      if (unsoldProducts.length === 0) {
        return [];
      }

      // Log all creation times to see what we're working with
      

      // Get the actual creation date from the first product (they should all have the same creation date)
      const firstProduct = unsoldProducts[0];
      if (!firstProduct) {
        return [];
      }
      
      const creationDate = new Date(firstProduct._creationTime);
      const dateKey = creationDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
      // Count owned vs consigned for this single date
      const ownedCount = unsoldProducts.filter((p: any) => 
        (p.ownershipType === "owned" || !p.ownershipType)
      ).length;
      const consignedCount = unsoldProducts.filter((p: any) => 
        p.ownershipType === "consigned"
      ).length;
      
      const result = [{
        month: dateKey,
        owned: ownedCount,
        consigned: consignedCount,
        all: unsoldProducts.length
      }];
      
      
      return result;
    }

    
    // Return empty array if no products or no valid creation dates
    return [];
  }, [dashboardData?.chartData?.inventory, inventoryData, activeFilter]);

  // Calculate filtered totals for the active filter tab
  const chartTotals = useMemo(() => {
    if (inventoryData?.products) {
      let filteredProducts = inventoryData.products;
      
      if (activeFilter === "listed") {
        filteredProducts = inventoryData.products.filter((p: any) => p.status === "active");
      } else if (activeFilter === "unlisted") {
        filteredProducts = inventoryData.products.filter((p: any) => p.status === "draft");
      }
      // "all" shows all products that haven't been sold
      
      // Only count products that haven't been sold (active or draft status)
      const unsoldProducts = filteredProducts.filter((p: any) => 
        p.status === "active" || p.status === "draft"
      );
      
      const ownedCount = unsoldProducts.filter((p: any) => 
        (p.ownershipType === "owned" || !p.ownershipType)
      ).length;
      const consignedCount = unsoldProducts.filter((p: any) => 
        p.ownershipType === "consigned"
      ).length;
      
      return {
        owned: ownedCount,
        consigned: consignedCount,
      };
    }
    
    return {
      owned: metrics.ownedProducts || 0,
      consigned: metrics.consignedProducts || 0,
    };
  }, [inventoryData, activeFilter, metrics.ownedProducts, metrics.consignedProducts]);

  // Don't render until filters are loaded to prevent flash of default data
  if (!isLoaded) {
    return (
      <div className="p-6 bg-neutral-50 dark:bg-neutral-950">
        <DashboardLoader message="Loading dashboard..." />
      </div>
    );
  }

  // Check for seller access errors and show appropriate message
  if (user.userType !== "seller") {
    return (
      <div className="p-6 flex items-center justify-center min-h-screen bg-neutral-50 dark:bg-neutral-950">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Seller Access Required</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You need to apply and be approved as a seller to access the seller dashboard.
            </p>
          </div>
          <div className="space-y-3">
            <SellerApplicationModal>
              <Button className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors block text-center">
                Apply to Become a Seller
              </Button>
            </SellerApplicationModal>
            <a
              href="/dashboard"
              className="w-full bg-secondary text-secondary-foreground px-6 py-3 rounded-lg hover:bg-secondary/80 transition-colors block text-center"
            >
              Go to Consumer Dashboard
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="container mx-auto px-6 py-6 space-y-6">
        {/* Filters Header - This stays static */}
        <DashboardFiltersHeader
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onClearFilters={handleClearFilters}
        />

        {/* Dashboard Content - This will refresh when filters change */}
        <DashboardContent
          key={JSON.stringify(backendFilters)} // Force re-render when filters change
          dashboardData={dashboardData}
          sellerMetrics={sellerMetrics}
          metrics={metrics}
          chartData={chartData}
          chartTotals={chartTotals}
          chartConfig={chartConfig}
          activeChart={activeChart}
          setActiveChart={setActiveChart}
          activeFilter={activeFilter}
          setActiveFilter={setActiveFilter}
          formatCurrency={formatCurrency}
          isFilteredDataLoading={isFilteredDataLoading}
          setIsFilteredDataLoading={setIsFilteredDataLoading}
          supplierSummary={supplierSummary}
        />
      </div>
    </div>
  );
}

// Separate component for content that refreshes with filters
function DashboardContent({
  dashboardData,
  sellerMetrics,
  metrics,
  chartData,
  chartTotals,
  chartConfig,
  activeChart,
  setActiveChart,
  activeFilter,
  setActiveFilter,
  formatCurrency,
  isFilteredDataLoading,
  setIsFilteredDataLoading,
  supplierSummary
}: {
  dashboardData: any;
  sellerMetrics: any;
  metrics: any;
  chartData: any;
  chartTotals: any;
  chartConfig: any;
  activeChart: "owned" | "consigned";
  setActiveChart: (chart: "owned" | "consigned") => void;
  activeFilter: "all" | "unlisted" | "listed";
  setActiveFilter: (filter: "all" | "unlisted" | "listed") => void;
  formatCurrency: (amount: number) => string;
  isFilteredDataLoading: boolean;
  setIsFilteredDataLoading: (loading: boolean) => void;
  supplierSummary: any;
}) {
  // Effect to reset loading state when data is loaded
  useEffect(() => {
    if (dashboardData !== undefined && sellerMetrics !== undefined) {
      setIsFilteredDataLoading(false);
    }
  }, [dashboardData, sellerMetrics, setIsFilteredDataLoading]);

  // Check for errors in the data
  if (dashboardData?.error || sellerMetrics?.error) {
    const errorMessage = dashboardData?.error || sellerMetrics?.error;
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md">
          <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Error Loading Dashboard</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {errorMessage}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (dashboardData === undefined || sellerMetrics === undefined) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            {isFilteredDataLoading ? "Applying filters..." : "Loading filtered results..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`transition-opacity duration-300 ${isFilteredDataLoading ? 'opacity-50' : 'opacity-100'}`}>
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Sales */}
          <Card className="bg-primary text-primary-foreground border-0 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-primary-foreground/80 uppercase tracking-wide">
                  <ShoppingCart className="w-4 h-4 mr-2 inline" />
                  Total Sales
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-primary-foreground">
                {metrics.totalSales}
              </div>
            </CardContent>
          </Card>

          {/* Total Revenue */}
          <Card className="bg-accent text-accent-foreground border-0 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-accent-foreground/80 uppercase tracking-wide">
                  <DollarSign className="w-4 h-4 mr-2 inline" />
                  Total Revenue
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-accent-foreground">
                {formatCurrency(metrics.totalRevenue)}
              </div>
            </CardContent>
          </Card>

          {/* Total Cost */}
          <Card className="bg-secondary text-secondary-foreground border-0 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-secondary-foreground/80 uppercase tracking-wide">
                  <Calculator className="w-4 h-4 mr-2 inline" />
                  Total Cost
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-secondary-foreground">
                {formatCurrency(metrics.totalCost)}
              </div>
            </CardContent>
          </Card>

          {/* Gross Profit */}
          <Card className="bg-muted text-foreground border-0 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-light text-foreground/70 uppercase tracking-wide">
                  <DollarSign className="w-4 h-4 mr-2 inline" />
                  Gross Profit
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">
                {formatCurrency(metrics.grossProfit)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
          {/* Left Column - Inventory Value & Chart */}
          <div className="space-y-6">
            {/* Combined Inventory Card */}
            <Card className="rounded-2xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300 py-0">
              <CardHeader className="flex flex-col items-stretch border-b !p-0 sm:flex-row">
                <div className="flex flex-1 flex-col justify-center gap-1 px-6 pt-4 pb-3 sm:!py-0">
                  <CardTitle className="text-primary text-lg font-light">Value of Inventory</CardTitle>
                  <div className="text-2xl font-bold text-primary mb-1">
                    {formatCurrency(metrics.inventoryValue)}
                  </div>
                </div>
                <div className="flex">
                  {(["owned", "consigned"] as const).map((key) => (
                    <button
                      key={key}
                      data-active={activeChart === key}
                      className="data-[active=true]:bg-muted/50 relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6"
                      onClick={() => setActiveChart(key)}
                    >
                      <span className="text-muted-foreground text-xs font-light">
                        {chartConfig[key].label}
                      </span>
                      <span className="text-lg leading-none font-bold sm:text-3xl">
                        {chartTotals[key].toLocaleString()}
                      </span>
                    </button>
                  ))}
                </div>
              </CardHeader>
              <CardContent className="px-6 pt-4 pb-6">
                {/* Filter Tabs */}
                <Tabs value={activeFilter} onValueChange={(value) => {

                  setActiveFilter(value as "all" | "unlisted" | "listed");
                }} className="w-full mb-6">
                  <TabsList className="grid w-full grid-cols-3 bg-primary/5 rounded-xl border border-border">
                    <TabsTrigger 
                      value="all" 
                      className="rounded-xl font-light data-[state=active]:bg-accent data-[state=active]:text-accent-foreground text-foreground transition-all duration-300"
                    >
                      All
                    </TabsTrigger>
                    <TabsTrigger 
                      value="unlisted"
                      className="rounded-xl font-light data-[state=active]:bg-accent data-[state=active]:text-accent-foreground text-foreground transition-all duration-300"
                    >
                      Unlisted
                    </TabsTrigger>
                    <TabsTrigger 
                      value="listed"
                      className="rounded-xl font-light data-[state=active]:bg-accent data-[state=active]:text-accent-foreground text-foreground transition-all duration-300"
                    >
                      Listed
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                {/* Chart */}
                {(() => {
                  return chartData && chartData.length > 0 ? (
                    <>
                      <ChartContainer
                        config={chartConfig}
                        className="aspect-auto h-[250px] w-full text-primary"
                      >
                        <BarChart
                          accessibilityLayer
                          data={chartData}
                          className="text-primary"
                          margin={{
                            left: 12,
                            right: 12,
                          }}
                        >
                          <CartesianGrid vertical={false} stroke="hsl(var(--border))" strokeOpacity={0.3} />
                          <XAxis
                            dataKey="month"
                            tickLine={false}
                            axisLine={false}
                            tickMargin={8}
                            minTickGap={32}
                            className="text-xs font-light text-primary"
                          />
                          <ChartTooltip
                            content={
                              <ChartTooltipContent
                                className="w-[150px] text-primary"
                                nameKey="inventory"
                                labelFormatter={(value) => value}
                              />
                            }
                          />
                          <Bar 
                            dataKey={activeChart} 
                            fill={`var(--color-${activeChart})`}
                            radius={[2, 2, 0, 0]}
                          />
                        </BarChart>
                      </ChartContainer>
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-[250px] text-center">
                      <div className="text-muted-foreground">
                        <BarChart3 className="w-12 h-12 mx-auto mb-3 opacity-50" />
                        <p className="text-sm font-light">No products added yet</p>
                        <p className="text-xs text-muted-foreground mt-1">Add your first product to see inventory trends</p>
                      </div>
                    </div>
                  );
                })()}
              </CardContent>
            </Card>

          </div>

          {/* Right Column - Other KPIs & Inventory Breakdown */}
          <div className="lg:col-span-2 space-y-6">
            {/* Other KPIs */}
            <Card className="rounded-2xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground">Other KPIs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-light text-sm uppercase tracking-wide text-primary mb-4">Average</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-light text-foreground">Gross Margin:</span>
                        <span className="font-light text-foreground">{metrics.averageGrossMargin.toFixed(2)}%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-light text-foreground">Transaction Value:</span>
                        <span className="font-light text-foreground">{formatCurrency(metrics.averageTransactionValue)}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-light text-sm uppercase tracking-wide text-primary mb-4">Inventory Sell Through Rate</h4>
                    <p className="text-sm font-light mb-3">Sold through 0% of beginning inventory</p>
                    <div className="flex justify-between items-center text-lg">
                      <span className="font-light text-foreground">0 / 0</span>
                      <span className="font-light text-foreground">0%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Supplier Summary */}
            {supplierSummary && (
              <Card className="rounded-2xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Supplier Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{supplierSummary.totalSuppliers}</div>
                      <p className="text-xs text-muted-foreground">Total Suppliers</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{supplierSummary.activeSuppliers}</div>
                      <p className="text-xs text-muted-foreground">Active</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{formatCurrency(supplierSummary.totalOutstanding)}</div>
                      <p className="text-xs text-muted-foreground">Outstanding</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{formatCurrency(supplierSummary.overdueAmount)}</div>
                      <p className="text-xs text-muted-foreground">Overdue</p>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-light text-foreground">Total Spent with Suppliers:</span>
                      <span className="font-medium text-foreground">{formatCurrency(supplierSummary.totalSpent)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Inventory Breakdown */}
            <Card className="rounded-2xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground">Inventory Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <h4 className="font-light text-sm uppercase tracking-wide text-primary mb-4">Owned vs Consigned</h4>
                                      <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-light text-foreground">Owned Products:</span>
                        <span className="font-light text-foreground">{metrics.ownedProducts}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-light text-foreground">Consigned Products:</span>
                        <span className="font-light text-foreground">{metrics.consignedProducts}</span>
                      </div>
                    </div>
                  
                  {/* Legend */}
                  <div className="flex items-center gap-4 mt-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-accent rounded-full"></div>
                      <span className="text-xs font-light">Owned</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-secondary rounded-full"></div>
                      <span className="text-xs font-light">Consigned</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
  );
}
