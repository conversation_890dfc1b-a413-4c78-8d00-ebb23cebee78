{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@convex-dev/auth": "^0.0.88", "@convex-dev/better-auth": "^0.8.0-alpha.0", "@hookform/resolvers": "^5.1.1", "@repo/backend": "workspace:*", "@repo/ui": "workspace:*", "@stripe/react-stripe-js": "^3.9.1", "@stripe/stripe-js": "^7.8.0", "@tiptap/extension-image": "^3.2.2", "@tiptap/extension-link": "^3.2.2", "@tiptap/extension-placeholder": "^3.2.2", "@tiptap/react": "^3.2.2", "@tiptap/starter-kit": "^3.2.2", "better-auth": "^1.3.4", "convex": "^1.25.4", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "lucide-react": "^0.475.0", "next": "^15.3.0", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "^19.1.0", "react-day-picker": "^9.8.1", "react-dom": "^19.1.0", "react-dropzone": "^14.2.9", "react-hook-form": "^7.60.0", "recharts": "^3.1.2", "sonner": "^2.0.6", "stripe": "^18.4.0", "zod": "^3.25.76"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.8", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "postcss": "^8.5.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "5.8.3"}}