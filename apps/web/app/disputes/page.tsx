import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { DisputeForm } from "@/components/disputes/DisputeForm";
import { InlineLoader } from "@/components/common";

export const metadata: Metadata = {
  title: "Dispute Center | HauteVault",
  description: "Submit and track disputes for your HauteVault transactions and interactions.",
  openGraph: {
    title: "Dispute Center | HauteVault",
    description: "Submit and track disputes for your HauteVault transactions and interactions.",
    type: "website",
  },
};

export default function DisputeCenterPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800">
      {/* Header */}
      <div className="border-b bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-light text-zinc-900 dark:text-zinc-100 mb-2">
              Dispute Center
            </h1>
            <p className="text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
              Need help with a transaction or have an issue with our platform? 
              Submit a dispute and our support team will assist you in resolving it.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Suspense fallback={<InlineLoader message="Loading dispute form..." />}>
          <DisputeForm />
        </Suspense>
      </div>

      {/* Footer Info */}
      <div className="max-w-7xl mx-auto px-6 py-8 border-t bg-white/50 dark:bg-zinc-900/50">
        <div className="grid md:grid-cols-3 gap-6 text-center">
          <div>
            <h3 className="font-semibold text-zinc-900 dark:text-zinc-100 mb-2">Quick Response</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400">
              Most disputes are reviewed within 24-48 hours
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-zinc-900 dark:text-zinc-100 mb-2">Fair Resolution</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400">
              Our team ensures fair and impartial dispute resolution
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-zinc-900 dark:text-zinc-100 mb-2">Expert Support</h3>
            <p className="text-sm text-zinc-600 dark:text-zinc-400">
              Dedicated support specialists handle your disputes
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}