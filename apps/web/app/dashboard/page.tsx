"use client";

import { DashboardLanding } from "@/components/dashboard";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useRouter } from "next/navigation";
import { Button } from "@repo/ui/components/button";
import { Lock } from "lucide-react";
import { FullScreenLoader } from "@/components/common";

export default function DashboardPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Loading State */}
      <AuthLoading>
        <FullScreenLoader message="Loading dashboard..." theme="light" />
      </AuthLoading>

      {/* Authenticated Users - Show Dashboard Landing */}
      <Authenticated>
        <DashboardLanding />
      </Authenticated>

      {/* Unauthenticated Users - Redirect to login */}
      <Unauthenticated>
        <div className="min-h-screen flex items-center justify-center">
          <div className="max-w-md mx-auto text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-br from-zinc-200 to-zinc-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <Lock className="w-8 h-8 text-black" />
            </div>
            
            <h1 className="text-3xl font-bold mb-4">Access Required</h1>
            <p className="text-zinc-400 mb-8">
              Please sign in to access your dashboard.
            </p>
            
            <Button
              className="w-full bg-white text-black hover:bg-zinc-200 font-medium"
              onClick={() => router.push("/")}
            >
              Sign In
            </Button>
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}
