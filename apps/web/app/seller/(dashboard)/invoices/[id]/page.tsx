"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Separator } from "@repo/ui/components/separator";
import { useMutation, useQuery, useAction } from "convex/react";
import { Download, Mail, Eye, DollarSign, ArrowLeft, Calendar, MapPin, Phone, Mail as MailIcon, Building2, Loader2, Globe } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import Link from "next/link";
import { ProfileImage } from "@/components/common/ProfileImage";

export default function InvoiceDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const [isSending, setIsSending] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const { id } = React.use(params);
  const invoiceId = id as Id<"invoices">;
  
  const invoice = useQuery(api.invoices.getInvoice, { invoiceId });
  const sendInvoiceEmail = useAction(api.invoices.sendInvoiceEmail);
  const getStorageUrl = useAction(api.invoices.getStorageUrl);
  const updateStatus = useMutation(api.invoices.updateInvoiceStatus);

  const handleSendEmail = async () => {
    setIsSending(true);
    try {
      await sendInvoiceEmail({ invoiceId });
      toast.success("Invoice sent successfully!");
    } catch (error) {
      toast.error("Failed to send invoice");
      console.error(error);
    } finally {
      setIsSending(false);
    }
  };

  const handleMarkAsPaid = async () => {
    try {
      await updateStatus({ invoiceId, status: "paid" });
      toast.success("Invoice marked as paid!");
    } catch (error) {
      toast.error("Failed to mark invoice as paid");
    }
  };

  // Helper function to convert image URL to base64
  const getImageAsBase64 = async (url: string): Promise<{ data: string; format: string } | null> => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      
      // Determine image format
      const format = blob.type.includes('png') ? 'PNG' : 'JPEG';
      
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve({ 
          data: reader.result as string, 
          format 
        });
        reader.onerror = () => resolve(null);
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("Failed to convert image to base64:", error);
      return null;
    }
  };

  const handleGeneratePDF = async () => {
    if (!invoice) return;
    setIsGeneratingPDF(true);
    
    try {
      console.log("Generating PDF for invoice:", invoice.invoiceNumber);
      toast.info("Generating PDF... This may take a moment.");
      
      // Import jsPDF dynamically
      const { jsPDF } = await import("jspdf");
      
      // Generate PDF with proper dimensions
      const pdfDoc = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdfDoc.internal.pageSize.getWidth();
      const pageHeight = pdfDoc.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (2 * margin);
      
      let yPosition = 30;
      
      // Load company logo if available
      let logoImage: { data: string; format: string } | null = null;
      if (invoice.sellerProfile?.companyLogo) {
        try {
          const logoUrl = await getStorageUrl({ storageId: invoice.sellerProfile.companyLogo });
          if (logoUrl) {
            logoImage = await getImageAsBase64(logoUrl);
          }
        } catch (error) {
          console.warn("Failed to load company logo for PDF:", error);
        }
      }
      
      // Header - Company Logo and Name
      let logoWidth = 0;
      if (logoImage) {
        try {
          // Add company logo (15mm width, maintain aspect ratio)
          const logoSize = 15;
          pdfDoc.addImage(logoImage.data, logoImage.format, margin, yPosition - 5, logoSize, logoSize);
          logoWidth = logoSize + 5; // Add some spacing after logo
        } catch (error) {
          console.warn("Failed to add logo to PDF:", error);
        }
      }
      
      pdfDoc.setFontSize(24);
      pdfDoc.setTextColor(48, 41, 35); // Primary dark color
      
      if (invoice.sellerProfile?.businessName) {
        pdfDoc.text(invoice.sellerProfile.businessName.toUpperCase(), margin + logoWidth, yPosition);
      } else {
        pdfDoc.text("HauteVault", margin + logoWidth, yPosition);
        pdfDoc.setFontSize(12);
        pdfDoc.setTextColor(151, 148, 135); // Secondary sage color
        pdfDoc.text("Luxury Marketplace", margin + logoWidth, yPosition + 8);
      }
      
      // Invoice badge and number (right side)
      pdfDoc.setFillColor(133, 107, 99); // Accent mauve color
      pdfDoc.roundedRect(pageWidth - margin - 40, yPosition - 5, 40, 12, 3, 3, 'F');
      pdfDoc.setTextColor(255, 255, 255);
      pdfDoc.setFontSize(10);
      pdfDoc.text("INVOICE", pageWidth - margin - 35, yPosition + 3);
      
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.setFontSize(20);
      pdfDoc.text(`#${invoice.invoiceNumber}`, pageWidth - margin - 35, yPosition + 20);
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      pdfDoc.text(`Invoice Date: ${formatDate(invoice.updatedAt)}`, pageWidth - margin - 35, yPosition + 30);
      
      yPosition += 50;
      
      // Separator line
      pdfDoc.setDrawColor(133, 107, 99);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 20;
      
      // Billing Information - Two columns
      const col1X = margin;
      const col2X = margin + (contentWidth / 2) + 10;
      
      // From section
      pdfDoc.setFontSize(12);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text("FROM", col1X, yPosition);
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      
      let fromY = yPosition + 8;
      
      if (invoice.sellerProfile) {
        // Company name
        pdfDoc.text(invoice.sellerProfile.businessName || "HauteVault Seller", col1X, fromY);
        fromY += 8;
        
        // Company email
        if (invoice.sellerProfile.companyEmail) {
          pdfDoc.text(invoice.sellerProfile.companyEmail, col1X, fromY);
          fromY += 8;
        }
        
        // Company phone
        if (invoice.sellerProfile.companyPhone || invoice.sellerProfile.phone) {
          pdfDoc.text(invoice.sellerProfile.companyPhone || invoice.sellerProfile.phone, col1X, fromY);
          fromY += 8;
        }
        
        // Company address
        if (invoice.sellerProfile.address) {
          pdfDoc.text(invoice.sellerProfile.address.street, col1X, fromY);
          fromY += 8;
          pdfDoc.text(`${invoice.sellerProfile.address.city}, ${invoice.sellerProfile.address.state} ${invoice.sellerProfile.address.zipCode}`, col1X, fromY);
          fromY += 8;
          pdfDoc.text(invoice.sellerProfile.address.country, col1X, fromY);
          fromY += 8;
        }
        
        // Website
        if (invoice.sellerProfile.website) {
          pdfDoc.text(invoice.sellerProfile.website, col1X, fromY);
          fromY += 8;
        }
      } else {
        // Fallback to default
        pdfDoc.text("HauteVault Seller", col1X, fromY);
        fromY += 8;
        pdfDoc.text("<EMAIL>", col1X, fromY);
        fromY += 8;
        pdfDoc.text("+****************", col1X, fromY);
        fromY += 8;
        pdfDoc.text("123 Luxury Lane", col1X, fromY);
        fromY += 8;
        pdfDoc.text("Beverly Hills, CA 90210", col1X, fromY);
        fromY += 8;
        pdfDoc.text("United States", col1X, fromY);
      }
      
      // Bill To section
      pdfDoc.setFontSize(12);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text("BILL TO", col2X, yPosition);
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      pdfDoc.text(invoice.clientName, col2X, yPosition + 8);
      pdfDoc.text(invoice.clientEmail, col2X, yPosition + 16);
      if (invoice.clientPhone) {
        pdfDoc.text(invoice.clientPhone, col2X, yPosition + 24);
      }
      pdfDoc.text(invoice.clientAddress.street, col2X, yPosition + 32);
      pdfDoc.text(`${invoice.clientAddress.city}, ${invoice.clientAddress.state} ${invoice.clientAddress.zipCode}`, col2X, yPosition + 40);
      pdfDoc.text(invoice.clientAddress.country, col2X, yPosition + 48);
      
      yPosition += 70;
      
      // Invoice Details - Three columns in a box
      const detailBoxY = yPosition;
      const detailBoxHeight = 25;
      pdfDoc.setFillColor(243, 243, 235, 0.1); // Primary light with transparency
      pdfDoc.roundedRect(margin, detailBoxY, contentWidth, detailBoxHeight, 3, 3, 'F');
      
      const detailCol1X = margin + 15;
      const detailCol2X = margin + (contentWidth / 3) + 15;
      const detailCol3X = margin + (2 * contentWidth / 3) + 15;
      
      pdfDoc.setFontSize(8);
      pdfDoc.setTextColor(151, 148, 135);
      pdfDoc.text("INVOICE DATE", detailCol1X, detailBoxY + 8);
      pdfDoc.text("DUE DATE", detailCol2X, detailBoxY + 8);
      pdfDoc.text("PAYMENT TERMS", detailCol3X, detailBoxY + 8);
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text(formatDate(invoice.updatedAt), detailCol1X, detailBoxY + 18);
      pdfDoc.text(invoice.dueDate ? formatDate(invoice.dueDate) : 'Not set', detailCol2X, detailBoxY + 18);
      pdfDoc.text(invoice.paymentTerms || 'Net 30', detailCol3X, detailBoxY + 18);
      
      yPosition += 40;
      
      // Items Table
      pdfDoc.setFontSize(12);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text("ITEM DETAILS", margin, yPosition);
      yPosition += 15;
      
      // Table header
      pdfDoc.setFillColor(243, 243, 235, 0.1);
      pdfDoc.roundedRect(margin, yPosition, contentWidth, 15, 3, 3, 'F');
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      pdfDoc.text("Description", margin + 10, yPosition + 10);
      pdfDoc.text("Quantity", margin + 100, yPosition + 10);
      pdfDoc.text("Unit Price", margin + 140, yPosition + 10);
      pdfDoc.text("Amount", margin + 180, yPosition + 10);
      
      yPosition += 20;
      
      // Table row
      pdfDoc.setDrawColor(133, 107, 99);
      pdfDoc.setLineWidth(0.2);
      pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text(invoice.itemDescription, margin + 10, yPosition + 10);
      pdfDoc.text("1", margin + 100, yPosition + 10);
      pdfDoc.text(formatCurrency(invoice.salePrice), margin + 140, yPosition + 10);
      pdfDoc.text(formatCurrency(invoice.salePrice), margin + 180, yPosition + 10);
      
      yPosition += 30;
      
      // Totals section
      const totalsX = pageWidth - margin - 80;
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      
      pdfDoc.text("Subtotal:", totalsX, yPosition);
      pdfDoc.text(formatCurrency(invoice.salePrice), pageWidth - margin - 10, yPosition);
      yPosition += 8;
      
      if (invoice.tax && invoice.tax > 0) {
        pdfDoc.text("Tax:", totalsX, yPosition);
        pdfDoc.text(formatCurrency(invoice.tax), pageWidth - margin - 10, yPosition);
        yPosition += 8;
      }
      
      // Separator line
      pdfDoc.setDrawColor(133, 107, 99);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.line(totalsX, yPosition + 5, pageWidth - margin, yPosition + 5);
      yPosition += 15;
      
      // Total
      pdfDoc.setFontSize(14);
      pdfDoc.setTextColor(48, 41, 35);
      pdfDoc.text("Total:", totalsX, yPosition);
      pdfDoc.text(formatCurrency(invoice.totalAmount), pageWidth - margin - 10, yPosition);
      
      // Paid status if applicable
      if (invoice.status === "paid") {
        yPosition += 15;
        pdfDoc.setFontSize(10);
        pdfDoc.setTextColor(34, 197, 94); // Green color
        pdfDoc.text("Paid on:", totalsX, yPosition);
        pdfDoc.text(invoice.paidDate ? formatDate(invoice.paidDate) : 'N/A', pageWidth - margin - 10, yPosition);
      }
      
      yPosition += 30;
      
      // Notes section
      if (invoice.notes) {
        pdfDoc.setFontSize(12);
        pdfDoc.setTextColor(48, 41, 35);
        pdfDoc.text("NOTES", margin, yPosition);
        yPosition += 15;
        
        pdfDoc.setFillColor(243, 243, 235, 0.1);
        pdfDoc.roundedRect(margin, yPosition, contentWidth, 20, 3, 3, 'F');
        
        pdfDoc.setFontSize(10);
        pdfDoc.setTextColor(151, 148, 135);
        pdfDoc.text(invoice.notes, margin + 10, yPosition + 12);
        
        yPosition += 30;
      }
      
      // Footer
      pdfDoc.setDrawColor(133, 107, 99);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 15;
      
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(151, 148, 135);
      pdfDoc.text("Thank you for your business!", pageWidth / 2, yPosition, { align: 'center' });
      pdfDoc.text("Please make payment by the due date to avoid any late fees.", pageWidth / 2, yPosition + 8, { align: 'center' });
      
      // Download the PDF directly
      pdfDoc.save(`invoice-${invoice.invoiceNumber}.pdf`);
      toast.success("PDF generated and downloaded successfully!");
      
    } catch (error) {
      console.error("PDF generation failed:", error);
      toast.error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (!invoice) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading invoice...</p>
          </div>
        </div>
      </div>
    );
  }

  const isOverdue = invoice.status !== "paid" && invoice.dueDate && new Date(invoice.dueDate) < new Date();
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/seller/invoices">
                <Button variant="ghost" className="rounded-xl font-light">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  INVOICE #{invoice.invoiceNumber}
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Created {formatDate(invoice.updatedAt)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge
                variant={
                  invoice.status === "paid" ? "default" :
                  isOverdue ? "destructive" :
                  invoice.status === "sent" ? "secondary" : "outline"
                }
                className="px-3 py-1 text-sm font-light rounded-xl"
              >
                {isOverdue ? "Overdue" : invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
              </Badge>
              
              <Button
                variant="outline"
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF}
                className="rounded-xl font-light"
              >
                {isGeneratingPDF ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {isGeneratingPDF ? "Generating..." : "Generate PDF"}
              </Button>
              
              {invoice.status !== "paid" && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleSendEmail}
                    disabled={isSending}
                    className="rounded-xl font-light"
                  >
                    {isSending ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Mail className="w-4 h-4 mr-2" />
                    )}
                    {isSending ? "Sending..." : "Send Email"}
                  </Button>
                  
                  <Button
                    onClick={handleMarkAsPaid}
                    className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
                  >
                    <DollarSign className="w-4 h-4 mr-2" />
                    Mark as Paid
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Invoice Card */}
        <Card className="rounded-xl border-border shadow-lg">
          <CardContent className="p-8">
            {/* Invoice Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex items-center space-x-3">
                {invoice.sellerProfile?.companyLogo ? (
                  <ProfileImage
                    storageId={invoice.sellerProfile.companyLogo}
                    name={invoice.sellerProfile.businessName || "Company"}
                    size="lg"
                    variant="company"
                    className="rounded-xl"
                  />
                ) : (
                  <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-primary-foreground" />
                  </div>
                )}
                <div>
                  <h2 className="text-2xl font-light text-primary tracking-wide">
                    {invoice.sellerProfile?.businessName || "HauteVault"}
                  </h2>
                  <p className="text-sm text-muted-foreground font-light">
                    {invoice.sellerProfile?.businessName ? "Invoice" : "Luxury Marketplace"}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="inline-flex items-center px-4 py-2 bg-accent text-accent-foreground rounded-full text-sm font-light">
                  INVOICE
                </div>
                <h3 className="text-3xl font-light text-primary mt-2">#{invoice.invoiceNumber}</h3>
                <p className="text-muted-foreground text-sm mt-1 font-light">Invoice Date: {formatDate(invoice.updatedAt)}</p>
              </div>
            </div>

            <Separator className="my-8" />

            {/* Billing Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-8">
              {/* From */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">From</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">
                    {invoice.sellerProfile?.businessName || "HauteVault Seller"}
                  </p>
                  
                  {/* Company Email */}
                  {invoice.sellerProfile?.companyEmail && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <MailIcon className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.sellerProfile.companyEmail}</span>
                    </div>
                  )}
                  
                  {/* Company Phone */}
                  {(invoice.sellerProfile?.companyPhone || invoice.sellerProfile?.phone) && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="font-light">
                        {invoice.sellerProfile.companyPhone || invoice.sellerProfile.phone}
                      </span>
                    </div>
                  )}
                  
                  {/* Company Website */}
                  {invoice.sellerProfile?.website && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Globe className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.sellerProfile.website}</span>
                    </div>
                  )}
                  
                  {/* Company Address */}
                  {invoice.sellerProfile?.address && (
                    <div className="flex items-start text-muted-foreground text-sm">
                      <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                      <div>
                        <p className="font-light">{invoice.sellerProfile.address.street}</p>
                        <p className="font-light">
                          {invoice.sellerProfile.address.city}, {invoice.sellerProfile.address.state} {invoice.sellerProfile.address.zipCode}
                        </p>
                        <p className="font-light">{invoice.sellerProfile.address.country}</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Fallback for no seller profile */}
                  {!invoice.sellerProfile && (
                    <>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <MailIcon className="w-4 h-4 mr-2" />
                        <span className="font-light"><EMAIL></span>
                      </div>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <Phone className="w-4 h-4 mr-2" />
                        <span className="font-light">+****************</span>
                      </div>
                      <div className="flex items-start text-muted-foreground text-sm">
                        <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                        <div>
                          <p className="font-light">123 Luxury Lane</p>
                          <p className="font-light">Beverly Hills, CA 90210</p>
                          <p className="font-light">United States</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Bill To */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Bill To</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">{invoice.clientName}</p>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <MailIcon className="w-4 h-4 mr-2" />
                    <span className="font-light">{invoice.clientEmail}</span>
                  </div>
                  {invoice.clientPhone && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.clientPhone}</span>
                    </div>
                  )}
                  <div className="flex items-start text-muted-foreground text-sm">
                    <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                    <div>
                      <p className="font-light">{invoice.clientAddress.street}</p>
                      <p className="font-light">{invoice.clientAddress.city}, {invoice.clientAddress.state} {invoice.clientAddress.zipCode}</p>
                      <p className="font-light">{invoice.clientAddress.country}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-4 bg-primary/5 rounded-xl">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Invoice Date</p>
                  <p className="font-light text-primary">{formatDate(invoice.updatedAt)}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Due Date</p>
                  <p className={`font-light ${isOverdue ? 'text-destructive' : 'text-primary'}`}>
                    {invoice.dueDate ? formatDate(invoice.dueDate) : 'Not set'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Payment Terms</p>
                  <p className="font-light text-primary">{invoice.paymentTerms || 'Net 30'}</p>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Item Details</h4>
              <div className="border border-border rounded-xl overflow-hidden">
                <div className="bg-primary/5 px-6 py-4 grid grid-cols-12 gap-4 text-sm font-light text-muted-foreground">
                  <div className="col-span-6">Description</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Unit Price</div>
                  <div className="col-span-2 text-right">Amount</div>
                </div>
                
                <div className="px-6 py-4 grid grid-cols-12 gap-4 text-sm border-t border-border">
                  <div className="col-span-6">
                    <p className="font-light text-primary">{invoice.itemDescription}</p>
                    <p className="text-muted-foreground text-sm mt-1 font-light">Product/Service</p>
                  </div>
                  <div className="col-span-2 text-center text-primary font-light">1</div>
                  <div className="col-span-2 text-center text-primary font-light">{formatCurrency(invoice.salePrice)}</div>
                  <div className="col-span-2 text-right font-light text-primary">{formatCurrency(invoice.salePrice)}</div>
                </div>
              </div>
            </div>

            {/* Totals */}
            <div className="flex justify-end">
              <div className="w-80 space-y-3">
                <div className="flex justify-between text-sm text-muted-foreground font-light">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.salePrice)}</span>
                </div>
                
                {invoice.tax && invoice.tax > 0 && (
                  <div className="flex justify-between text-sm text-muted-foreground font-light">
                    <span>Tax:</span>
                    <span>{formatCurrency(invoice.tax)}</span>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between text-lg font-light text-primary">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.totalAmount)}</span>
                </div>
                
                {invoice.status === "paid" && (
                  <div className="flex justify-between text-sm text-green-600 font-light">
                    <span>Paid on:</span>
                    <span>{invoice.paidDate ? formatDate(invoice.paidDate) : 'N/A'}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {invoice.notes && (
              <>
                <Separator className="my-8" />
                <div>
                  <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-3">Notes</h4>
                  <div className="p-4 bg-primary/5 rounded-xl">
                    <p className="text-muted-foreground font-light">{invoice.notes}</p>
                  </div>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-12 pt-8 border-t border-border">
              <div className="text-center text-muted-foreground text-sm font-light">
                <p>Thank you for your business!</p>
                <p className="mt-1">Please make payment by the due date to avoid any late fees.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}