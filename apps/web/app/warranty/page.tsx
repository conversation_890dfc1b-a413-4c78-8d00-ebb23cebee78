"use client";

import { Card } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Shield<PERSON>heck, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export default function WarrantyPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-6 text-zinc-600 hover:text-zinc-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Dashboard
        </Button>

        <Card className="p-12 text-center bg-white">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <ShieldCheck className="w-8 h-8 text-blue-600" />
          </div>
          
          <h1 className="text-3xl font-bold text-zinc-900 mb-4">
            Warranty Check
          </h1>
          
          <p className="text-zinc-600 text-lg mb-8 max-w-2xl mx-auto">
            Verify the authenticity and warranty status of luxury products with our 
            advanced verification system. Protect your investments with comprehensive 
            product authentication services.
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-blue-800 mb-2">Coming Soon Features:</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Product authenticity verification</li>
              <li>• Warranty status checking</li>
              <li>• Serial number validation</li>
              <li>• Certificate of authenticity</li>
              <li>• Insurance claim support</li>
            </ul>
          </div>

          <Button
            onClick={() => router.push("/dashboard")}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Return to Dashboard
          </Button>
        </Card>
      </div>
    </div>
  );
}
