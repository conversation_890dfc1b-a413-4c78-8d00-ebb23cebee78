"use client";

import { Suspense } from "react";
import dynamic from "next/dynamic";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Button } from "@repo/ui/components/button";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@repo/ui/components/tabs";
import { Users, Plus } from "lucide-react";

// Dynamic imports to avoid SSR issues
const RedditForumHome = dynamic(
  () => import("@/components/forum/RedditForumHome"),
  { 
    ssr: false,
    loading: () => <ForumSkeleton />
  }
);

const EnhancedForumHome = dynamic(
  () => import("@/components/forum/EnhancedForumHome"),
  { 
    ssr: false,
    loading: () => <ForumSkeleton />
  }
);

export default function ForumPage() {
  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <Tabs defaultValue="reddit" className="w-full">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">HauteVault Community</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Connect, share, and learn with fellow luxury enthusiasts
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => window.location.href = "/forum/groups"}>
                <Users className="mr-2 h-4 w-4" />
                Browse Communities
              </Button>
              <Button onClick={() => window.location.href = "/forum/submit"}>
                <Plus className="mr-2 h-4 w-4" />
                Create Post
              </Button>
            </div>
          </div>

          <TabsContent value="reddit" className="mt-0">
            <RedditForumHome />
          </TabsContent>
          
          {/* <TabsContent value="classic" className="mt-0">
            <EnhancedForumHome />
          </TabsContent> */}
        </Tabs>
      </div>
    </CommunityLayout>
  );
}

function ForumSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex gap-4 mb-6">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-24 rounded-lg" />
        ))}
      </div>
    </div>
  );
}
