# HauteVault Forum System

A comprehensive Reddit/Facebook group-style forum system for the HauteVault luxury marketplace community.

## 🎯 Overview

The forum system enables members to:
- Create and join topic-based groups
- Post threads with rich text and images
- Engage in threaded discussions with nested comments
- Share knowledge and connect with other members

## ✨ Key Features

### 1. **Forum Groups**
- **Topic-based communities** - Create groups around specific interests (e.g., Authentication, Sneakers, Luxury Watches)
- **Public & Private groups** - Control group visibility and membership
- **Group moderation** - Assign moderators to manage group content
- **Custom branding** - Add icons, colors, and banner images to groups
- **Group rules** - Set community guidelines for each group

### 2. **Rich Content Posts**
- **Rich text editor** - Format posts with bold, italic, headings, lists, quotes, and code blocks
- **Image uploads** - Attach multiple images to posts
- **Categories** - Organize posts by type (General, Marketplace, Questions, Deals, etc.)
- **Tags** - Add searchable tags to posts
- **Sticky posts** - Pin important posts to the top

### 3. **Threaded Comments**
- **Nested replies** - Create conversation threads with unlimited nesting
- **Best answer** - Post authors can mark the best answer to their questions
- **Like system** - Upvote helpful comments
- **Comment editing** - Edit comments with visible edit history

### 4. **Search & Discovery**
- **Full-text search** - Search posts by title and content
- **Category filters** - Browse posts by category
- **Sort options** - Sort by recent, popular, most viewed, or most commented
- **Group filtering** - View posts from specific groups

## 📁 File Structure

```
packages/backend/convex/
├── forum.ts              # Main forum backend functions
├── members.ts            # Member profile integration
└── schema.ts            # Database schema with forum tables

apps/web/
├── app/forum/
│   ├── page.tsx         # Main forum page
│   ├── groups/
│   │   └── page.tsx     # Groups listing page
│   └── post/[id]/
│       └── page.tsx     # Individual post page
└── components/forum/
    ├── EnhancedForumHome.tsx  # Main forum component with groups
    ├── ForumGroups.tsx        # Groups listing component
    ├── ForumHome.tsx          # Basic forum component
    ├── ForumPostDetail.tsx    # Post detail view
    └── RichTextEditor.tsx     # TipTap rich text editor
```

## 🗄️ Database Schema

### Forum Groups
```typescript
forumGroups: {
  name: string
  slug: string
  description: string
  icon?: string
  color?: string
  memberCount: number
  postCount: number
  isActive: boolean
  isPrivate: boolean
  createdBy: userId
  moderators: userId[]
  rules?: string
  bannerImage?: storageId
}
```

### Forum Posts
```typescript
forumPosts: {
  userId: userId
  groupId?: forumGroupId
  title: string
  content: string (HTML)
  contentPlainText?: string
  images: storageId[]
  category: string
  tags: string[]
  isPinned: boolean
  isLocked: boolean
  isStickied: boolean
  views: number
  likes: number
  commentCount: number
}
```

### Forum Comments
```typescript
forumComments: {
  postId: forumPostId
  userId: userId
  content: string
  parentCommentId?: commentId
  likes: number
  isBestAnswer: boolean
  isEdited: boolean
  isDeleted: boolean
}
```

## 🔧 API Functions

### Group Management
- `createForumGroup` - Create a new forum group
- `getForumGroups` - List all groups
- `getForumGroup` - Get single group details
- `joinForumGroup` - Join a group
- `leaveForumGroup` - Leave a group

### Post Management  
- `createForumPost` - Create a new post with images
- `getForumPosts` - Get paginated posts with filters
- `getForumPost` - Get single post with details
- `updateForumPost` - Edit an existing post
- `toggleForumPostLike` - Like/unlike a post

### Comment Management
- `addForumComment` - Add a comment to a post
- `getForumComments` - Get threaded comments
- `markBestAnswer` - Mark comment as best answer

### File Upload
- `generateUploadUrl` - Get secure URL for image uploads

## 🎨 UI Components

### Rich Text Editor
The forum uses TipTap for rich text editing with support for:
- Text formatting (bold, italic, heading)
- Lists (bullet, numbered)
- Quotes and code blocks
- Links and images
- Undo/redo functionality

### Forum Groups UI
- Grid layout with group cards
- Member count and post statistics
- Join/leave functionality
- Group creation modal

### Post Display
- Author profile with avatar
- Rich text content rendering
- Image gallery
- Engagement metrics (likes, views, comments)
- Category and tag badges

## 🚀 Usage Examples

### Creating a Group
```typescript
const group = await createForumGroup({
  name: "Sneaker Authentication",
  slug: "sneaker-auth",
  description: "Discussion about authenticating luxury sneakers",
  icon: "👟",
  color: "#FF6B6B",
  isPrivate: false,
  rules: "Be respectful, no spam"
});
```

### Creating a Post with Images
```typescript
// 1. Generate upload URL
const uploadUrl = await generateUploadUrl();

// 2. Upload images
const imageIds = await uploadImages(files, uploadUrl);

// 3. Create post
const post = await createForumPost({
  groupId: group._id,
  title: "How to spot fake Jordan 1s",
  content: "<p>Here are the key things to look for...</p>",
  contentPlainText: "Here are the key things to look for...",
  images: imageIds,
  category: "authentication",
  tags: ["jordan", "authentication", "guide"]
});
```

### Adding a Comment
```typescript
const comment = await addForumComment({
  postId: post._id,
  content: "Great guide! One more tip...",
  parentCommentId: undefined // Top-level comment
});
```

## 🔐 Security & Permissions

- **Authentication required** - Users must be logged in to post
- **Group membership** - Only members can post in groups
- **Post ownership** - Only authors can edit/delete their posts
- **Moderation** - Group moderators can manage content
- **Private groups** - Restricted visibility and membership

## 🎯 Best Practices

1. **Use groups** for topic organization
2. **Add images** to make posts more engaging
3. **Tag posts** for better discoverability
4. **Mark best answers** in Q&A threads
5. **Use rich formatting** for readability
6. **Set group rules** to maintain quality

## 📈 Future Enhancements

- [ ] Real-time notifications for replies
- [ ] User reputation system
- [ ] Advanced moderation tools
- [ ] Post drafts and auto-save
- [ ] Polls and surveys
- [ ] Private messaging between members
- [ ] Email digest of popular posts
- [ ] Mobile app support

## 🛠️ Maintenance

### Database Indexes
The forum system uses multiple indexes for performance:
- Posts by group, category, and activity
- Comments by post and parent
- Groups by slug and member count

### Image Storage
Images are stored in Convex storage with secure URLs. Consider implementing:
- Image compression before upload
- CDN integration for faster delivery
- Automatic cleanup of unused images

### Performance Tips
- Implement virtual scrolling for long comment threads
- Cache frequently accessed groups
- Use pagination for all list views
- Optimize image loading with lazy loading
